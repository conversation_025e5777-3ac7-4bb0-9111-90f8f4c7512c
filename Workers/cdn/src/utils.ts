import { enableLog } from './config';

// 日志记录函数
export const log = (message: string, error: Error | null = null): void => {
	if (enableLog) {
		error ? console.error(message, error) : console.log(message);
	}
};

// HMAC 函数
export async function hmac(key: string | Uint8Array, message: string, encoding?: 'hex'): Promise<string | Uint8Array> {
	const encoder = new TextEncoder();
	const keyData = (typeof key === 'string') ? encoder.encode(key) : key;
	const messageData = encoder.encode(message);

	const cryptoKey = await crypto.subtle.importKey(
		"raw",
		keyData,
		{ name: "HMAC", hash: "SHA-256" },
		false,
		["sign"]
	);

	const signature = await crypto.subtle.sign("HMAC", cryptoKey, messageData);
	if (encoding === 'hex') {
		return Array.from(new Uint8Array(signature))
			.map(b => b.toString(16).padStart(2, '0'))
			.join('');
	}
	return new Uint8Array(signature);
}

// 获取哈希值
export async function getHash(message: string): Promise<string> {
	const encoder = new TextEncoder();
	const data = encoder.encode(message);
	const hashBuffer = await crypto.subtle.digest('SHA-256', data);
	const hashArray = Array.from(new Uint8Array(hashBuffer));
	return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// 获取日期
export function getDate(timestamp: number): string {
	const date = new Date(timestamp * 1000);
	const year = date.getUTCFullYear();
	const month = ("0" + (date.getUTCMonth() + 1)).slice(-2);
	const day = ("0" + date.getUTCDate()).slice(-2);
	return `${year}-${month}-${day}`;
}
