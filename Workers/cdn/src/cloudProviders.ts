import { workersKV } from './config';
import { log, hmac, getHash, getDate } from './utils';

// 自定义 CloudFlare 接口响应
interface CloudflareResponse {
	success: boolean;
	errors?: any[];
	result?: any;
}

// 阿里云 DCDN 请求
export async function aliyunDcdnRequest(ipList: string) {
	const [aliyunAccessKeyID, aliyunAccessKeySecret, aliyunDcdnDomainArrayStr] = await Promise.all([
		workersKV.get('AliyunAccessKeyID'),
		workersKV.get('AliyunAccessKeySecret'),
		workersKV.get('AliyunDcdnDomainArray')
	]);
	const aliyunDcdnDomainArray = JSON.parse(aliyunDcdnDomainArrayStr || '[]');

	if (!aliyunAccessKeyID || !aliyunAccessKeySecret || !aliyunDcdnDomainArray.length) {
		log('Aliyun configuration is incomplete');
		return { error_msg: "500 Aliyun configuration not complete" };
	}

	const action = 'BatchSetDcdnDomainConfigs';
	const version = '2018-01-15';
	const timestamp = new Date().toISOString().replace(/\.\d{3}Z$/, 'Z');
	const signatureNonce = crypto.randomUUID();
	const host = 'dcdn.aliyuncs.com';
	const method = 'POST';
	const path = '/';

	const domainNames = aliyunDcdnDomainArray.join(',');
	const functions = JSON.stringify([{
		functionArgs: [{ argName: 'ip_list', argValue: ipList }],
		functionName: 'ip_allow_list_set'
	}]);

	const body = `DomainNames=${encodeURIComponent(domainNames)}&Functions=${encodeURIComponent(functions)}`;

	const contentSha256 = await getHash(body);

	const headers: Record<string, string> = {
		'host': host,
		'x-acs-action': action,
		'x-acs-version': version,
		'x-acs-date': timestamp,
		'x-acs-signature-nonce': signatureNonce,
		'x-acs-content-sha256': contentSha256,
		'content-type': 'application/x-www-form-urlencoded'
	};

	const canonicalizedHeaders = Object.keys(headers)
		.sort()
		.map(key => `${key}:${headers[key]}`)
		.join('\n') + '\n';

	const signedHeaders = Object.keys(headers).sort().join(';');

	const canonicalRequest = `${method}\n${path}\n\n${canonicalizedHeaders}\n${signedHeaders}\n${contentSha256}`;

	const stringToSign = `ACS3-HMAC-SHA256\n${await getHash(canonicalRequest)}`;

	const signature = await hmac(aliyunAccessKeySecret, stringToSign, 'hex');

	headers['Authorization'] = `ACS3-HMAC-SHA256 Credential=${aliyunAccessKeyID},SignedHeaders=${signedHeaders},Signature=${signature}`;

	try {
		const response = await fetch(`https://${host}${path}`, {
			method,
			headers,
			body
		});
		return await response.json();
	} catch (error) {
		log('Aliyun DCDN fetch error:', error as Error);
		return { error_msg: "500 Fetch error" };
	}
}

// Cloudflare 请求
export async function cloudflareRequest(method: string, endpoint: string, body: any = null) {
	const [cloudflareAccountId, cloudflareListId, cloudflareToken] = await Promise.all([
		workersKV.get('CloudflareAccountId'),
		workersKV.get('CloudflareListId'),
		workersKV.get('CloudflareToken')
	]);

	if (!cloudflareAccountId || !cloudflareListId || !cloudflareToken) {
		throw new Error('Cloudflare configuration is missing');
	}

	const url = `https://api.cloudflare.com/client/v4/accounts/${cloudflareAccountId}/rules/lists/${cloudflareListId}/${endpoint}`;
	const headers: Record<string, string> = {
		'Authorization': `Bearer ${cloudflareToken}`,
		'Content-Type': 'application/json'
	};

	const options: RequestInit = { method, headers };
	if (body) options.body = JSON.stringify(body);

	try {
		const response = await fetch(url, options);
		const data = await response.json() as CloudflareResponse;
		if (!data.success) {
			log(`Cloudflare API error: ${JSON.stringify(data.errors)}`);
			throw new Error('Cloudflare API request failed');
		}
		return data;
	} catch (error) {
		log(`Error during Cloudflare ${method} request to ${endpoint}:`, error as Error);
		throw error;
	}
}

// 添加 IP 到 Cloudflare
export async function addIpToCloudflare(ip: string, comment: string) {
	return cloudflareRequest('POST', 'items', [{ comment, ip }]);
}

// 从 Cloudflare 删除 IP
export async function deleteIpFromCloudflare(ip: string) {
	const currentIpList = await getCloudflareIpList();
	const item = currentIpList.find((item: { ip: string; id: string }) => item.ip === ip);
	if (!item) throw new Error('IP not found in Cloudflare list');
	return cloudflareRequest('DELETE', 'items', { items: [{ id: item.id }] });
}

// 获取 Cloudflare IP 列表
export async function getCloudflareIpList() {
	const data = await cloudflareRequest('GET', 'items');
	return data.result || [];
}

// 同步 Cloudflare IP 列表
export async function syncCloudflareIpList(ipList: string) {
	const currentIpList = await getCloudflareIpList();
	const currentIps = currentIpList.map((item: { ip: string }) => item.ip);
	const newIps = ipList.split(',');

	const addedIps = [];
	const deletedIps = [];

	for (const ip of newIps) {
		if (!currentIps.includes(ip)) {
			await addIpToCloudflare(ip, 'Added by Workers');
			addedIps.push(ip);
		}
	}

	for (const item of currentIpList) {
		if (!newIps.includes((item as { ip: string }).ip)) {
			await deleteIpFromCloudflare((item as { ip: string }).ip);
			deletedIps.push((item as { ip: string }).ip);
		}
	}

	return { added: addedIps, deleted: deletedIps };
}

// 腾讯云 CDN 请求
export async function tencentCloudCdnRequest(ipList: string) {
	const [tencentCloudSecretId, tencentCloudSecretKey, tencentCloudCdnDomainArrayStr] = await Promise.all([
		workersKV.get('TencentCloudSecretId'),
		workersKV.get('TencentCloudSecretKey'),
		workersKV.get('TencentCloudCdnDomainArray')
	]);
	const tencentCloudCdnDomainArray = JSON.parse(tencentCloudCdnDomainArrayStr || '[]');

	if (!tencentCloudSecretId || !tencentCloudSecretKey || !tencentCloudCdnDomainArray.length) {
		log('Tencent Cloud configuration is missing');
		return { error_msg: "500 Tencent Cloud configuration not complete" };
	}

	const host = "cdn.tencentcloudapi.com";
	const service = "cdn";
	const action = "UpdateDomainConfig";
	const version = "2018-06-06";
	const timestamp = Math.floor(Date.now() / 1000);
	const date = getDate(timestamp);

	const results = [];

	for (const domain of tencentCloudCdnDomainArray) {
		const payload = JSON.stringify({
			Domain: domain,
			IpFilter: {
				Switch: "on",
				FilterType: "whitelist",
				Filters: ipList.split(',')
			}
		});

		const canonicalHeaders = "content-type:application/json; charset=utf-8\nhost:" + host + "\n";
		const signedHeaders = "content-type;host";
		const hashedRequestPayload = await getHash(payload);
		const canonicalRequest = `POST\n/\n\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}`;

		const algorithm = "TC3-HMAC-SHA256";
		const hashedCanonicalRequest = await getHash(canonicalRequest);
		const credentialScope = `${date}/${service}/tc3_request`;
		const stringToSign = `${algorithm}\n${timestamp}\n${credentialScope}\n${hashedCanonicalRequest}`;

		const kDate = await hmac("TC3" + tencentCloudSecretKey, date);
		const kService = await hmac(kDate, service);
		const kSigning = await hmac(kService, "tc3_request");
		const signature = await hmac(kSigning, stringToSign, "hex");

		const authorization = `${algorithm} Credential=${tencentCloudSecretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

		const headers = {
			Authorization: authorization,
			"Content-Type": "application/json; charset=utf-8",
			Host: host,
			"X-TC-Action": action,
			"X-TC-Timestamp": timestamp.toString(),
			"X-TC-Version": version
		};

		try {
			const response = await fetch(`https://${host}`, {
				method: "POST",
				headers: headers,
				body: payload
			});
			const data = await response.json();
			results.push({ domain, response: data });
		} catch (error) {
			log(`Error updating Tencent Cloud CDN for domain ${domain}:`, error as Error);
			results.push({ domain, error: (error as Error).message });
		}
	}

	return results;
}
