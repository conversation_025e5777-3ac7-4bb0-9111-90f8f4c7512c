:root {
	--ds-background-default: #FFFFFF;
	--ds-background-neutral: #F4F5F7;
	--ds-text: #172B4D;
	--ds-text-subtle: #6B778C;
	--ds-border: #DFE1E6;
	--ds-brand: #0052CC;
	--ds-brand-bold: #0747A6;
	--ds-surface: #FFFFFF;
	--ds-surface-raised: #FFFFFF;
	--ds-surface-sunken: #F4F5F7;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
	background-color: var(--ds-background-default);
	color: var(--ds-text);
}

.navbar {
	background-color: var(--ds-surface);
	padding: 0.5rem 1rem;
	border-bottom: 1px solid #DFE1E6;
}

.navbar-brand {
	color: var(--ds-brand);
	font-weight: bold;
}

.card {
	background-color: var(--ds-surface-raised);
	border: 1px solid var(--ds-border);
	border-radius: 3px;
	box-shadow: 0 1px 1px rgba(9, 30, 66, 0.13);
	margin-bottom: 1.5rem;
}

.card-header {
	background-color: var(--ds-surface-sunken);
	border-bottom: 1px solid var(--ds-border);
	padding: 0.75rem 1.25rem;
}

.card-title {
	color: var(--ds-text);
	font-size: 1rem;
	font-weight: 500;
	margin-bottom: 0;
}

.card-body {
	padding: 1.25rem;
}

#jsonOutput {
	background-color: var(--ds-background-neutral);
	border: 1px solid var(--ds-border);
	border-radius: 3px;
	color: var(--ds-text);
	font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
	font-size: 0.875rem;
	padding: 1rem;
	white-space: pre-wrap;
}

.ip-input-group {
	margin-bottom: 0.5rem;
}

.btn {
	font-size: 0.875rem;
	font-weight: 500;
	padding: 0.375rem 0.75rem;
	border-radius: 3px;
	transition: background-color 0.1s ease-out;
}

.btn-primary {
	background-color: var(--ds-brand);
	border-color: var(--ds-brand);
}

.btn-primary:hover {
	background-color: var(--ds-brand-bold);
	border-color: var(--ds-brand-bold);
}

.btn-outline-secondary {
	color: var(--ds-text-subtle);
	border-color: var(--ds-border);
}

.btn-outline-secondary:hover {
	color: var(--ds-text);
	background-color: var(--ds-background-neutral);
	border-color: var(--ds-text-subtle);
}

.badge {
	font-size: 0.75rem;
	font-weight: 500;
	padding: 0.25em 0.5em;
	border-radius: 3px;
}

.form-control {
	font-size: 0.875rem;
	border: 2px solid var(--ds-border);
	border-radius: 3px;
	padding: 0.375rem 0.75rem;
}

.form-control:focus {
	border-color: var(--ds-brand);
	box-shadow: 0 0 0 2px rgba(0, 82, 204, 0.2);
}
