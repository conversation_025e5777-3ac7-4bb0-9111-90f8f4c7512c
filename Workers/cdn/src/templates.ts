import { workersKV } from './config';
import { log } from './utils';

// 渲染管理 IP 页面
export const renderManageIPPage = async (): Promise<Response> => {
	try {
		const ipList = await workersKV.get('ipList') || '';
		const html = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Zhong <PERSON>i's Eye (CDN)</title>
            <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/fastbootstrap/fastbootstrap.min.css?2.2.0"/>
            <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/bootstrap/icons/bootstrap-icons.min.css?1.11.3"/>
            <link rel="stylesheet" href="https://tie3.lilh.net/priv/workers/cdn/styles.css"/>
        </head>
        <body>
            <nav class="navbar">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#"><i class="bi bi-eye-fill me-2"></i>Zhong Kui's Eye (CDN)</a>
                </div>
            </nav>

            <div class="container mt-4">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title"><i class="bi bi-list-ul me-2"></i>IP List</h5>
                                <span class="badge bg-secondary" id="ipCount">0 IPs</span>
                            </div>
                            <div class="card-body">
                                <form id="ipForm" novalidate>
                                    <div id="ipListContainer" class="mb-3">
                                        <!-- IP inputs will be dynamically added here -->
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" id="addIpButton" class="btn btn-outline-secondary">
                                            <i class="bi bi-plus-circle me-2"></i>Add New IP
                                        </button>
                                        <button type="submit" class="btn btn-primary" id="saveButton">
                                            <i class="bi bi-save me-2"></i>Save Changes
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title"><i class="bi bi-braces me-2"></i>API Response</h5>
                            </div>
                            <div class="card-body">
                                <div id="jsonOutput" class="mb-3">Waiting for API response...</div>
                                <div class="d-flex justify-content-between">
                                    <button id="refreshButton" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-2"></i>Refresh Page
                                    </button>
                                    <button id="sipButton" class="btn btn-outline-secondary">
                                        <i class="bi bi-box-arrow-right me-2"></i>Execute /sip
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script src="https://tie3.lilh.net/pub/static/js/bootstrap/bootstrap.bundle.min.js?5.3.3"></script>
            <script>
                // 获取DOM元素
                const ipListContainer = document.getElementById('ipListContainer');
                const addIpButton = document.getElementById('addIpButton');
                const jsonOutput = document.getElementById('jsonOutput');
                const refreshButton = document.getElementById('refreshButton');
                const sipButton = document.getElementById('sipButton');
                const saveButton = document.getElementById('saveButton');
                const ipCountBadge = document.getElementById('ipCount');
                const ipForm = document.getElementById('ipForm');

                // IPv4地址正则表达式
                const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

                // 防抖函数
                const debounce = (func, wait) => {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                };

                // 验证IP输入的函数
                const validateIpInput = (input) => {
                    input.setCustomValidity('');
                    if (!ipv4Regex.test(input.value)) {
                        input.setCustomValidity('Please enter a valid IPv4 address.');
                        input.classList.add('is-invalid');
                        input.classList.remove('is-valid');
                    } else {
                        input.classList.remove('is-invalid');
                        input.classList.add('is-valid');
                    }
                    input.checkValidity();
                };

                // 防抖处理的验证函数
                const debouncedValidateIpInput = debounce(validateIpInput, 300);

                // 添加IP输入框的函数
                const addIpInput = (value = '') => {
                    const inputGroup = document.createElement('div');
                    inputGroup.className = 'input-group ip-input-group mb-3';
                    inputGroup.innerHTML = \`
                        <input type="text" class="form-control ip-input" placeholder="Enter IP address" value="\${value}" required pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                        <button class="btn btn-outline-danger" type="button">
                            <i class="bi bi-trash"></i>
                        </button>
                        <div class="invalid-feedback w-100">Please enter a valid IPv4 address.</div>
                    \`;
                    ipListContainer.appendChild(inputGroup);

                    const input = inputGroup.querySelector('input');
                    input.addEventListener('input', () => debouncedValidateIpInput(input));
                    input.addEventListener('blur', () => validateIpInput(input));

                    inputGroup.querySelector('button').addEventListener('click', () => {
                        ipListContainer.removeChild(inputGroup);
                        updateIpCount();
                    });

                    updateIpCount();
                };

                // 更新IP数量的函数
                const updateIpCount = () => {
                    const count = document.querySelectorAll('.ip-input').length;
                    ipCountBadge.textContent = \`\${count} IP\${count !== 1 ? 's' : ''}\`;
                };

                // 初始化IP列表
                '${ipList}'.split(',').filter(ip => ip.trim() !== '').forEach(ip => addIpInput(ip.trim()));

                // 添加IP按钮事件监听器
                addIpButton.addEventListener('click', () => addIpInput());

                // 表单提交事件监听器
                ipForm.addEventListener('submit', async (event) => {
                    event.preventDefault();
                    event.stopPropagation();

                    const allInputsValid = Array.from(document.querySelectorAll('.ip-input'))
                        .every(input => ipv4Regex.test(input.value.trim()));

                    if (ipForm.checkValidity() && allInputsValid) {
                        saveButton.disabled = true;
                        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Saving...';

                        try {
                            const ipList = Array.from(document.querySelectorAll('.ip-input'))
                                .map(input => input.value.trim())
                                .filter(ip => ipv4Regex.test(ip))
                                .join(',');

                            const formData = new FormData();
                            formData.append('ipList', ipList);

                            const response = await fetch('/mip', {
                                method: 'POST',
                                body: formData
                            });
                            const jsonResponse = await response.json();
                            jsonOutput.textContent = JSON.stringify(jsonResponse, null, 2);
                        } catch (error) {
                            console.error('Error:', error);
                            jsonOutput.textContent = 'Failed to save IP list.';
                        } finally {
                            saveButton.disabled = false;
                            saveButton.innerHTML = '<i class="bi bi-save me-2"></i>Save Changes';
                        }
                    }

                    ipForm.classList.add('was-validated');
                });

                // 刷新按钮事件监听器
                refreshButton.addEventListener('click', () => {
                    location.reload();
                });

                // SIP按钮事件监听器
                sipButton.addEventListener('click', async () => {
                    try {
                        sipButton.disabled = true;
                        sipButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Executing...';
                        const response = await fetch('/sip');
                        const jsonResponse = await response.json();
                        jsonOutput.textContent = JSON.stringify(jsonResponse, null, 2);

                        if (jsonResponse.msg && jsonResponse.msg.includes('Added')) {
                            const newIp = jsonResponse.msg.split(' ')[0];
                            addIpInput(newIp);
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        jsonOutput.textContent = 'Failed to execute SIP request.';
                    } finally {
                        sipButton.disabled = false;
                        sipButton.innerHTML = '<i class="bi bi-box-arrow-right me-2"></i>Execute /sip';
                    }
                });

                // 初始化IP数量
                updateIpCount();
            </script>
        </body>
        </html>
        `;
		return new Response(html, {
			headers: { 'Content-Type': 'text/html' }
		});
	} catch (error) {
		log('Error fetching IP list:', error instanceof Error ? error : new Error(String(error)));
		return new Response('{"error_msg":"500 Internal Server Error"}', { status: 500 });
	}
};
