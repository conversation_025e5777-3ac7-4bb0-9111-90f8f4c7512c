// Trie 数据结构实现
class TrieNode {
	children: Map<string, TrieNode> = new Map();
	isEndOfPattern: boolean = false;
}

class Trie {
	root: TrieNode = new TrieNode();

	insert(pattern: string): void {
		let node = this.root;
		for (const char of pattern) {
			if (!node.children.has(char)) {
				node.children.set(char, new TrieNode());
			}
			node = node.children.get(char)!;
		}
		node.isEndOfPattern = true;
	}

	search(str: string): boolean {
		let node = this.root;
		for (const char of str) {
			if (!node.children.has(char)) {
				return false;
			}
			node = node.children.get(char)!;
		}
		return node.isEndOfPattern;
	}
}

// 初始化 Trie
const githubTrie = new Trie();
githubTrie.insert('github.com');
githubTrie.insert('raw.githubusercontent.com');
githubTrie.insert('gist.github.com');

// 常量定义
const PREFIX = '/';

// 预检请求的响应头设置
const PREFLIGHT_INIT: ResponseInit = {
	status: 204,
	headers: new Headers({
		'access-control-allow-origin': '*',
		'access-control-allow-methods': 'GET,POST,PUT,PATCH,TRACE,DELETE,HEAD,OPTIONS',
		'access-control-max-age': '1728000',
	}),
};

// 创建自定义响应的辅助函数
function makeRes(body: string, status = 200, headers: Record<string, string> = {}): Response {
	headers['access-control-allow-origin'] = '*';
	return new Response(body, { status, headers });
}

// 检查URL是否匹配GitHub模式的辅助函数
function checkUrl(u: string): boolean {
	const url = new URL(u);
	return githubTrie.search(url.hostname);
}

// 主事件监听器
addEventListener('fetch', (event: FetchEvent) => {
	event.respondWith(
		handleRequest(event.request).catch(
			err => makeRes(`ClouFlare Worker Error:\n${err.stack}`, 502)
		)
	);
});

// 主要请求处理函数
async function handleRequest(req: Request): Promise<Response> {
	const url = new URL(req.url);
	let path = url.searchParams.get('q');

	// 处理重定向
	if (path) {
		return Response.redirect(`https://${url.host}${PREFIX}${path}`, 301);
	}

	// 提取实际路径
	path = url.href.slice(url.origin.length + PREFIX.length).replace(/^https?:\/+/, 'https://');

	// 检查是否为有效的GitHub URL
	if (checkUrl(path)) {
		return httpHandler(req, path);
	} else {
		return makeRes('Not a valid GitHub URL', 400);
	}
}

// HTTP请求处理函数
async function httpHandler(req: Request, pathname: string): Promise<Response> {
	// 处理预检请求
	if (req.method === 'OPTIONS' && req.headers.has('access-control-request-headers')) {
		return new Response(null, PREFLIGHT_INIT);
	}

	const reqHeaders = new Headers(req.headers);
	let urlStr = pathname.startsWith('https://') ? pathname : `https://${pathname}`;
	const urlObj = new URL(urlStr);

	// 准备代理请求
	const reqInit: RequestInit = {
		method: req.method,
		headers: reqHeaders,
		redirect: 'manual',
		body: req.body
	};

	return proxy(urlObj, reqInit);
}

// 代理请求处理函数
async function proxy(urlObj: URL, reqInit: RequestInit): Promise<Response> {
	const res = await fetch(urlObj.href, reqInit);
	const resHeaders = new Headers(res.headers);

	// 处理重定向
	if (resHeaders.has('location')) {
		const location = resHeaders.get('location');
		if (location && checkUrl(location)) {
			resHeaders.set('location', PREFIX + location);
		} else if (location) {
			const newUrlObj = new URL(location, urlObj);
			return proxy(newUrlObj, reqInit);
		}
	}

	// 设置CORS头
	resHeaders.set('access-control-expose-headers', '*');
	resHeaders.set('access-control-allow-origin', '*');

	// 移除可能的安全头
	['content-security-policy', 'content-security-policy-report-only', 'clear-site-data'].forEach(
		header => resHeaders.delete(header)
	);

	// 返回最终响应，不进行内容修改
	return new Response(res.body, {
		status: res.status,
		headers: resHeaders,
	});
}
