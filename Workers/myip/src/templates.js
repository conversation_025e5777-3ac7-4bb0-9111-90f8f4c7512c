export function htmlTemplate(aliCdnRealIp, cfConnectingIp) {
	return `
    <!DOCTYPE html>
    <html lang="zh">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>IP Origins</title>
        <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/fastbootstrap/fastbootstrap.min.css?2.2.0">
        <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/font-awesome/all.min.css?6.6.0">
        <link rel="stylesheet" href="https://tie3.lilh.net/priv/workers/myip/styles.css">
    </head>
    <body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">IP Origins</a>
            <div class="d-flex flex-grow-1 justify-content-center">
                <form id="ip-query-form" class="d-flex needs-validation" novalidate>
                    <input type="text" id="ip-query-input" class="form-control" placeholder="请输入 IPv4" required>
                </form>
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse flex-grow-0" id="navbarNav">
                <ul class="navbar-nav position-relative">
                    <div class="nav-link-background"></div>
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="china-nav">中国大陆</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="cloudflare-nav">CloudFlare</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="ipify-nav">IPify</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="batch-query-nav">批量查询</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div id="ip-info-content"></div>
        <div id="batch-query-content" style="display: none;">
			<form id="batch-query-form" class="needs-validation" novalidate>
				<div class="form-group">
					<textarea id="ip-list" class="form-control" rows="10" placeholder="每行输入一个 IPv4 地址" required></textarea>
					<div class="invalid-feedback">
						请至少输入一个有效的 IP 地址
					</div>
				</div>
				<button class="btn btn-primary mt-3" id="batchQuerySubmit" type="submit">查询</button>
			</form>
        </div>
    </div>

    <div class="toast-container position-fixed top-0 end-0 p-3">
      <div id="toastContainer"></div>
    </div>

    <div class="batch-modal-fullscreen" id="batchResultModal">
        <div class="batch-modal-content">
            <div class="batch-modal-header">
                <div id="batchQueryCount" class="batch-modal-query-count"></div>
                <button type="button" class="batch-modal-close-button" data-dismiss="modal">&times;</button>
            </div>
            <ul class="batch-modal-result-list" id="batchResultList"></ul>
        </div>
    </div>

    <div id="loadingSpinner" class="loading-spinner d-none">
        <svg class="spinner-icon" viewBox="0 0 24 24">
            <path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" opacity=".25"/>
            <path d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"/>
        </svg>
    </div>

    <script src="https://tie3.lilh.net/pub/static/js/bootstrap/bootstrap.bundle.min.js?5.3.3"></script>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // 获取DOM元素
        const navLinks = document.querySelectorAll('.nav-link');
        const navLinkBackground = document.createElement('div');
        navLinkBackground.classList.add('nav-link-background');
        document.querySelector('.navbar-nav').appendChild(navLinkBackground);
        const ipQueryForm = document.getElementById('ip-query-form');
        const ipQueryInput = document.getElementById('ip-query-input');
        const batchQueryForm = document.getElementById('batch-query-form');
        const ipList = document.getElementById('ip-list');
        const ipInfoContent = document.getElementById('ip-info-content');
        const batchQueryContent = document.getElementById('batch-query-content');
        const batchResultModal = document.getElementById('batchResultModal');
        const loadingSpinner = document.getElementById('loadingSpinner');

        // 更新导航链接背景
        function updateNavLinkBackground(activeLink) {
            const linkRect = activeLink.getBoundingClientRect();
            const navRect = activeLink.closest('.navbar-nav').getBoundingClientRect();

            navLinkBackground.style.width = \`\${linkRect.width}px\`;
            navLinkBackground.style.height = \`\${linkRect.height}px\`;
            navLinkBackground.style.left = \`\${linkRect.left - navRect.left}px\`;
            navLinkBackground.style.top = \`\${linkRect.top - navRect.top}px\`;
        }

        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // 导航链接点击事件处理
        navLinks.forEach(link => {
            link.addEventListener('click', async (e) => {
                e.preventDefault();
                navLinks.forEach(l => l.classList.remove('active'));
                e.currentTarget.classList.add('active');

                updateNavLinkBackground(e.currentTarget);

                const type = e.currentTarget.id.replace('-nav', '');
                if (type === 'batch-query') {
                    ipInfoContent.style.display = 'none';
                    batchQueryContent.style.display = 'block';
                } else {
                    ipInfoContent.style.display = 'block';
                    batchQueryContent.style.display = 'none';
                    showLoadingSpinner();
                    try {
                        await loadIPInfo(type);
                    } catch (error) {
                        console.error('加载 IP 信息失败:', error);
                    } finally {
                        hideLoadingSpinner();
                    }
                }
            });
        });

        // 初始化背景位置
        const activeLink = document.querySelector('.nav-link.active');
        if (activeLink) {
            updateNavLinkBackground(activeLink);
        }

        // 响应窗口大小变化
        window.addEventListener('resize', () => {
            const activeLink = document.querySelector('.nav-link.active');
            if (activeLink) {
                updateNavLinkBackground(activeLink);
            }
        });

        // 关闭批量查询结果模态框
        document.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', () => {
                batchResultModal.style.display = 'none';
            });
        });

        // 防抖函数
        const debounce = (func, delay) => {
            let timeoutId;
            return (...args) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func(...args), delay);
            };
        };

        // 验证单个IP地址
        const validateIP = (ip) => {
            const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return ipPattern.test(ip);
        };

        // 验证IP输入并更新UI
        const validateIpInput = (input) => {
            if (validateIP(input.value.trim())) {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            } else {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
            }
        };

        // 使用防抖处理IP输入验证
        const debouncedValidateIpInput = debounce(validateIpInput, 300);

        // IP查询表单提交事件处理
        ipQueryForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (validateIP(ipQueryInput.value.trim())) {
                ipQueryInput.classList.remove('is-invalid');
                ipQueryInput.classList.add('is-valid');
                loadingSpinner.classList.remove('d-none');
                await loadIPInfo('custom', ipQueryInput.value.trim());
                loadingSpinner.classList.add('d-none');
            } else {
                ipQueryInput.classList.remove('is-valid');
                ipQueryInput.classList.add('is-invalid');
            }
        });

        // IP输入实时验证
        ipQueryInput.addEventListener('input', () => {
            debouncedValidateIpInput(ipQueryInput);
        });

        // 提取IP地址列表
        const extractIPs = (input) => {
            const ipPattern = /(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/g;
            return input.match(ipPattern) || [];
        };

        // 验证IP列表并更新UI
        const validateIpList = (textarea) => {
            const ips = extractIPs(textarea.value);
            const validIPs = ips.filter(validateIP);
            if (validIPs.length > 0) {
                textarea.classList.remove('is-invalid');
                textarea.classList.add('is-valid');
            } else {
                textarea.classList.remove('is-valid');
                textarea.classList.add('is-invalid');
            }
        };

        // 使用防抖处理IP列表验证
        const debouncedValidateIpList = debounce(validateIpList, 300);

		// 批量查询表单提交事件处理
		batchQueryForm.addEventListener('submit', async (e) => {
			e.preventDefault();
			e.stopPropagation();

			const originalIPs = extractIPs(ipList.value);
			const originalCount = originalIPs.length;

			const uniqueIPs = Array.from(new Set(originalIPs));
			const validIPs = uniqueIPs.filter(validateIP);

			if (validIPs.length > 0) {
				ipList.classList.remove('is-invalid');
				ipList.classList.add('is-valid');
				loadingSpinner.classList.remove('d-none');

				// 更新文本框内容，只保留唯一的有效 IP
				ipList.value = validIPs.join('\\n');

				const results = await batchGetIPDetails(validIPs);
				displayBatchResults(results, originalCount);
				loadingSpinner.classList.add('d-none');
			} else {
				ipList.classList.remove('is-valid');
				ipList.classList.add('is-invalid');
			}

			// 添加这行来触发 FastBootstrap 的表单验证
			batchQueryForm.classList.add('was-validated');
		});

        // IP列表输入实时验证
        ipList.addEventListener('input', () => {
            debouncedValidateIpList(ipList);
        });

        // 显示加载动画
        function showLoadingSpinner() {
            document.getElementById('loadingSpinner').classList.remove('d-none');
        }

        // 隐藏加载动画
        function hideLoadingSpinner() {
            document.getElementById('loadingSpinner').classList.add('d-none');
        }

        // 加载IP信息
        async function loadIPInfo(type, customIP) {
            ipInfoContent.innerHTML = '';
            try {
                let ip;
                switch (type) {
                    case 'china':
                        ip = '${aliCdnRealIp}';
                        break;
                    case 'cloudflare':
                        ip = '${cfConnectingIp}';
                        break;
                    case 'ipify':
                        const ipifyResponse = await fetch('https://api.ipify.org?format=json');
                        const ipifyData = await ipifyResponse.json();
                        ip = ipifyData.ip;
                        break;
                    case 'custom':
                        ip = customIP || '';
                        break;
                    default:
                        console.error('未知的 IP 类型');
                        return;
                }

                if (!ip) {
                    console.error('无效的 IP 地址');
                    return;
                }

                const [ipInfoResponse, sceneInfoResponse, hostInfoResponse] = await Promise.all([
                    fetch(\`https://svc-api.lilh.net/ip/info?ip=\${encodeURIComponent(ip)}\`),
					fetch(\`https://svc-api.lilh.net/ip/scene?ip=\${encodeURIComponent(ip)}\`),
					fetch(\`https://svc-api.lilh.net/ip/host?ip=\${encodeURIComponent(ip)}\`)
				]);

				const [ipInfoData, sceneInfoData, hostInfoData] = await Promise.all([
					ipInfoResponse.json(),
					sceneInfoResponse.json(),
					hostInfoResponse.json()
				]);

				ipInfoContent.innerHTML = generateIPInfoContent(ip, ipInfoData.data, sceneInfoData.data, hostInfoData.data);
			} catch (error) {
				console.error('加载 IP 信息失败:', error);
				ipInfoContent.innerHTML = '<p>加载 IP 信息失败，请稍后重试。</p>';
				throw error;
			}
		}

		// 显示批量查询结果
		function displayBatchResults(results, originalCount) {
			const batchQueryCount = document.getElementById('batchQueryCount');
			const batchResultList = document.getElementById('batchResultList');
			const uniqueCount = results.length;

			batchQueryCount.textContent = \`共查询 \${uniqueCount} 个唯一 IP（原始输入 \${originalCount} 个 IP）\`;
			batchResultList.innerHTML = results.map(result => generateBatchQueryResultItem(result)).join('');
			batchResultModal.style.display = 'block';
		}

		// 切换AS详情显示
		window.toggleASDetails = function(element) {
			const resultItem = element.closest('.batch-modal-result-item');
			const details = resultItem.querySelector('.batch-modal-as-details');
			if (details) {
				if (details.style.display === 'none') {
					details.style.display = 'block';
					element.textContent = '隐藏 AS 信息';
				} else {
					details.style.display = 'none';
					element.textContent = '查看 AS 信息';
				}
			}
		}

		// 初始加载中国大陆IP信息
		loadIPInfo('china').catch(error => {
			console.error('初始加载 IP 信息失败:', error);
		});
	});

	// 批量获取IP详情
	async function batchGetIPDetails(ips) {
		return Promise.all(ips.map(async (ip) => {
			const [ipInfoResponse, sceneInfoResponse, hostInfoResponse] = await Promise.all([
				fetch(\`https://svc-api.lilh.net/ip/info?ip=\${encodeURIComponent(ip)}\`),
				fetch(\`https://svc-api.lilh.net/ip/scene?ip=\${encodeURIComponent(ip)}\`),
				fetch(\`https://svc-api.lilh.net/ip/host?ip=\${encodeURIComponent(ip)}\`)
			]);

			const [ipInfoData, sceneInfoData, hostInfoData] = await Promise.all([
				ipInfoResponse.json(),
				sceneInfoResponse.json(),
				hostInfoResponse.json()
			]);

			return {
				ip,
				ipInfo: ipInfoData.data,
				sceneInfo: sceneInfoData.data,
				hostInfo: hostInfoData.data
			};
		}));
	}

	// 生成IP信息内容
	function generateIPInfoContent(ip, ipInfo, sceneInfo, hostInfo) {
		return \`
			<div class="ip-info">
				<span class="ip-address" data-bs-toggle="tooltip" data-bs-placement="top" title="点击复制" onclick="copyToClipboard('\${ip}')">\${ip}</span>
				<div class="location">\${ipInfo.continent} / \${ipInfo.country}</div>
			</div>
			<div class="details">
				<div class="detail-item">
					<span class="detail-label highlight">来自于 :</span>
					<span class="detail-value">\${ipInfo.prov} \${ipInfo.city} \${ipInfo.district}</span>
				</div>
				<div class="detail-item">
					<span class="detail-label highlight">运营商 :</span>
					<span class="detail-value">\${ipInfo.isp}</span>
				</div>
				<div class="detail-item">
					<span class="detail-label highlight">应用场景 :</span>
					<span class="detail-value">\${sceneInfo.scene}</span>
				</div>
				<div class="detail-item">
					<span class="detail-label highlight">所属机构 :</span>
					<span class="detail-value">\${hostInfo.owner}</span>
				</div>
			</div>
			<div class="as-section">
				\${hostInfo.asInfo.map(as => \`
					<div class="as-item">
						<div class="as-header">
							<span class="as-number">AS \${as.asnumber}</span>
						</div>
						<div class="as-content">
							<div class="detail-item">
								<span class="detail-label">AS 名称 :</span>
								<span class="detail-value">\${as.asname}</span>
							</div>
							<div class="detail-item">
								<span class="detail-label">AS 运营商 :</span>
								<span class="detail-value">\${as.isp}</span>
							</div>
						</div>
					</div>
				\`).join('')}
			</div>
			\`;
	}

	// 显示提示信息
	window.showToast = (message, type = 'success') => {
		const toastEl = document.createElement('div');
		toastEl.className = \`toast align-items-center text-white border-0 toast-\${type}\`;
		toastEl.setAttribute('role', 'alert');
		toastEl.setAttribute('aria-live', 'assertive');
		toastEl.setAttribute('aria-atomic', 'true');

		toastEl.innerHTML = \`
				<div class="d-flex">
					<div class="toast-body">\${message}</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			\`;

		const toastContainer = document.getElementById('toastContainer');
		toastContainer.appendChild(toastEl);

		const toast = new bootstrap.Toast(toastEl);
		toast.show();

		toastEl.addEventListener('hidden.bs.toast', () => toastEl.remove());
	};

	// 复制到剪贴板
	window.copyToClipboard = function(text) {
		navigator.clipboard.writeText(text).then(() => {
			window.showToast('IP 已复制');
		}).catch(err => {
			console.error('无法复制 IP:', err);
			window.showToast('复制 IP 失败', 'error');
		});
	}

	// 生成批量查询结果项
	function generateBatchQueryResultItem(result) {
		const asInfo = result.hostInfo.asInfo.map(as => \`AS\${as.asnumber} - \${as.asname} (\${as.isp})\`).join('<br>');
		return \`
			<li class="batch-modal-result-item">
				<div class="batch-modal-result-header">
					<span class="batch-modal-ip-address">\${result.ip}</span>
					<span class="batch-modal-as-info" onclick="window.toggleASDetails(this)">查看 AS 信息</span>
				</div>
				<div class="batch-modal-result-details">
					<div class="batch-modal-detail-item">
						<span class="batch-modal-detail-label">来自于</span>
						<span class="batch-modal-detail-value">\${result.ipInfo.country} \${result.ipInfo.prov} \${result.ipInfo.city}</span>
					</div>
					<div class="batch-modal-detail-item">
						<span class="batch-modal-detail-label">运营商</span>
						<span class="batch-modal-detail-value">\${result.ipInfo.isp}</span>
					</div>
					<div class="batch-modal-detail-item">
						<span class="batch-modal-detail-label">所属机构</span>
						<span class="batch-modal-detail-value">\${result.hostInfo.owner}</span>
					</div>
					<div class="batch-modal-detail-item">
						<span class="batch-modal-detail-label">应用场景</span>
						<span class="batch-modal-detail-value">\${result.sceneInfo.scene}</span>
					</div>
				</div>
				<div class="batch-modal-as-details" style="display: none;">\${asInfo}</div>
			</li>
			\`;
	}
	</script>
	</body>
	</html>
	`;
}
