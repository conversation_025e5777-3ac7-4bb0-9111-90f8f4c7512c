:root {
	--atlassian-blue: #0052CC;
	--atlassian-blue-light: #4C9AFF;
	--ds-background-default: #1D2125;
	--ds-background-input: #22272B;
	--ds-text: #C7D1DB;
	--ds-border: #A1BDD914;
	--ds-error: #FF5630;
}

body {
	background-color: var(--ds-background-default);
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
	color: #FFFFFF;
}

.navbar {
	background-color: var(--ds-background-default);
	border-bottom: 1px solid #333C43;
}

.navbar-brand {
	margin-left: 1rem;
}

.navbar-nav {
	position: relative;
}

.nav-link-background {
	position: absolute;
	background-color: var(--atlassian-blue);
	transition: all 0.3s ease;
	z-index: 0;
	border-radius: 3px;
}

.nav-link {
	position: relative;
	z-index: 1;
	transition: color 0.3s ease;
}

.navbar .nav-link {
	position: relative;
	z-index: 1;
	background-color: transparent;
	transition: color 0.3s ease;
	color: #FFFFFF;
	padding: 0.5rem 1rem;
	border-radius: 3px;
	margin: 0 0.25rem;
}

.navbar .nav-link:hover {
	color: var(--atlassian-blue-light);
}

.navbar .nav-link.active {
	background-color: transparent;
	color: #FFFFFF;
}

.navbar .nav-link.active:hover {
	color: #FFFFFF;
}

.container {
	max-width: 1100px;
	margin: 40px auto;
	padding: 20px;
}

.ip-info {
	text-align: center;
	margin-bottom: 40px;
	position: relative;
}

.ip-address {
	font-size: 2.5em;
	font-weight: bold;
	cursor: pointer;
	position: relative;
	display: inline-block;
}

.ip-address::after {
	content: '';
	position: absolute;
	width: 100%;
	height: 3px;
	bottom: -4px;
	left: 0;
	background-color: #FFFFFF;
	transform: scaleX(0);
	transform-origin: bottom left;
	transition: transform 0.3s ease-out;
}

.ip-address:hover::after {
	transform: scaleX(1);
}

#ip-info-content {
	position: relative;
	min-height: 200px;
}

.location {
	font-size: 1.2em;
	font-weight: bold;
	margin-top: 10px;
}

.details {
	display: grid;
	grid-template-columns: auto auto;
	gap: 20px 100px;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
	padding: 0 15px;
	overflow: visible;
}

.detail-label {
	color: #B6C2CF;
	position: relative;
	white-space: nowrap;
	margin-right: 10px;
	flex-shrink: 0;
	padding-bottom: 2px;
}

.detail-label::after {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: -2px;
	height: 2px;
	background-color: rgba(159, 173, 188, 0.3);
}

.detail-value {
	color: #FFFFFF;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.highlight {
	color: #B6C2CF;
	text-shadow: 0 0 1px rgba(159, 173, 188, 0.5);
}

.detail-item:nth-child(odd) .detail-value {
	direction: rtl;
	text-align: left;
}

.detail-item:nth-child(even) .detail-value {
	direction: ltr;
	text-align: right;
}

.as-section {
	margin-top: 30px;
	width: 100%;
}

.as-item {
	background-color: #1D2125;
	border: 1px dashed #333C43;
	border-radius: 3px;
	padding: 16px;
	margin-bottom: 16px;
}

.as-header {
	margin-bottom: 12px;
}

.as-number {
	font-size: 1.1em;
	font-weight: 600;
	color: var(--atlassian-blue-light);
}

.as-content .detail-item {
	margin-bottom: 15px;
}

.as-content .detail-value {
	text-align: right;
}

#ip-query-input {
	background-color: var(--ds-background-input);
	border: 2px solid var(--ds-border);
	color: var(--ds-text);
	border-radius: 3px;
	padding: 6px 10px;
	font-size: 14px;
	width: 300px;
	margin-right: -255px;
}

#ip-query-input:focus {
	border-color: #4C9AFF;
	box-shadow: 0 0 0 2px #4C9AFF40;
	outline: none;
}

#ip-query-input::placeholder {
	color: #8C9BAB;
}

#ip-query-input.is-invalid {
	border-color: var(--ds-error);
	box-shadow: 0 0 0 2px rgba(255, 86, 48, 0.25);
}

#ip-query-input.is-valid {
	border-color: #36B37E;
	box-shadow: 0 0 0 2px rgba(54, 179, 126, 0.25);
}

@media (min-width: 992px) {
	#ip-query-form {
		margin-right: 30px;
	}
}

@media (max-width: 991px) {
	#ip-query-form {
		margin-right: 15px;
	}
}

#batch-query-content textarea {
	background-color: var(--ds-background-input);
	border: 2px solid var(--ds-border);
	color: var(--ds-text);
	border-radius: 3px;
	padding: 12px;
	font-size: 14px;
	resize: vertical;
	height: 500px;
	transition: background-color 0.3s ease, border-color 0.3s ease;
}

#batch-query-content textarea:hover,
#batch-query-content textarea:focus {
	border-color: var(--atlassian-blue-light);
}

#batch-query-content textarea:focus {
	box-shadow: 0 0 0 2px rgba(76, 154, 255, 0.25);
	outline: none;
}

#batch-query-content textarea.is-invalid {
	border-color: var(--ds-error);
	box-shadow: 0 0 0 2px rgba(255, 86, 48, 0.25);
}

#batch-query-content textarea.is-valid {
	border-color: #36B37E;
	box-shadow: 0 0 0 2px rgba(54, 179, 126, 0.25);
}

.batch-modal-fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(9, 30, 66, 0.54);
	z-index: 1050;
	display: none;
	overflow-y: auto;
}

.batch-modal-content {
	background-color: #ffffff;
	border-radius: 3px;
	box-shadow: 0 0 0 1px rgba(9, 30, 66, 0.08), 0 2px 4px rgba(9, 30, 66, 0.13);
	margin: 48px auto;
	max-width: 1400px;
	padding: 20px 30px;
	position: relative;
}

.batch-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.batch-modal-query-count {
	font-weight: 600;
	color: #172B4D;
	margin: 0;
}

.batch-modal-close-button {
	background: none;
	border: none;
	color: #6B778C;
	font-size: 24px;
	cursor: pointer;
	padding: 0;
	line-height: 1;
}

.batch-modal-result-item {
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid #E9E9E9;
}

.batch-modal-result-list {
	padding: 0;
	margin: 0;
	list-style-type: none;
}

.batch-modal-result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.batch-modal-ip-address {
	font-size: 18px;
	font-weight: 500;
	color: #172B4D;
}

.batch-modal-as-info {
	font-size: 12px;
	color: #0052CC;
	cursor: pointer;
	text-decoration: none;
}

.batch-modal-result-details {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	padding: 8px;
	gap: 16px 70px;
}

.batch-modal-detail-item {
	display: flex;
	flex-direction: column;
}

.batch-modal-detail-label {
	font-size: 12px;
	color: #6B778C;
	margin-bottom: 4px;
}

.batch-modal-detail-value {
	font-size: 14px;
	color: #172B4D;
}

.batch-modal-as-details {
	background-color: #EAE6FF;
	margin-top: 12px;
	margin-bottom: 0;
	padding: 8px 12px;
	border-radius: 3px;
	font-size: 12px;
	color: #403294;
	line-height: 1.5;
}

.loading-spinner {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 9999;
}

.spinner-icon {
	animation: rotate 2s linear infinite;
	width: 25px;
	height: 25px;
}

.spinner-icon path {
	fill: #0052CC;
}

.spinner-icon path:first-child {
	fill: #4C9AFF;
}

@keyframes rotate {
	100% {
		transform: rotate(360deg);
	}
}

.toast-container {
	z-index: 1060;
}

.toast-success {
	background-color: #28a789;
}

.toast-error {
	background-color: #dc355c;
}

.toast-warning {
	background-color: #ffa807;
	color: #212529;
}

.toast-info {
	background-color: #17a2b8;
}
