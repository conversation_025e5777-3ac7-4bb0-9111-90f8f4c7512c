import { htmlTemplate } from './templates';

export default {
	async fetch(request) {
		const url = new URL(request.url);
		const path = url.pathname;

		// 处理 API 请求
		if (path.startsWith('/api/')) {
			return handleApiRequest(request);
		}

		// 获取请求头信息
		const aliCdnRealIp = request.headers.get('ali-cdn-real-ip') || 'UnknownIP';
		const cfConnectingIp = request.headers.get('cf-connecting-ip') || 'UnknownIP';

		// 返回包含 HTML 和客户端 JavaScript 的响应
		return new Response(htmlTemplate(aliCdnRealIp, cfConnectingIp), {
			headers: { 'Content-Type': 'text/html' },
		});
	},
};

async function handleApiRequest(request) {
	const url = new URL(request.url);
	const path = url.pathname;

	if (path === '/api/ip/china') {
		return new Response(request.headers.get('ali-cdn-real-ip') || 'UnknownIP');
	} else if (path === '/api/ip/cloudflare') {
		return new Response(request.headers.get('cf-connecting-ip') || 'UnknownIP');
	}

	// 如果没有匹配的路径，返回 404
	return new Response('Not Found', { status: 404 });
}
