// Docker镜像仓库主机地址
const DEFAULT_HUB_HOST = 'registry-1.docker.io';
// Docker认证服务器地址
const AUTH_URL = 'https://auth.docker.io';
// 自定义的工作服务器地址
let workersUrl = 'https://docker.mirror.lilh.net/';

// 定义路由表
const ROUTES: { [key: string]: string } = {
	"quay": "quay.io",
	"gcr": "gcr.io",
	"k8s-gcr": "k8s.gcr.io",
	"k8s": "registry.k8s.io",
	"ghcr": "ghcr.io",
	"cloudsmith": "docker.cloudsmith.io",
	"nvcr": "nvcr.io",
	"test": "registry-1.docker.io",
};

/**
 * 根据主机名选择对应的上游地址
 * @param {string} host 主机名
 * @returns {string} 返回上游地址
 */
function routeByHosts(host: string): string {
	return ROUTES[host] || DEFAULT_HUB_HOST;
}

/** @type {RequestInit} */
const PREFLIGHT_INIT: RequestInit = {
	// 预检请求配置
	headers: new Headers({
		'access-control-allow-origin': '*',
		'access-control-allow-methods': 'GET,POST,PUT,PATCH,TRACE,DELETE,HEAD,OPTIONS',
		'access-control-max-age': '1728000',
	}),
}

export default {
	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);
		workersUrl = `https://${url.hostname}`;

		// 获取请求参数中的 ns
		const ns = url.searchParams.get('ns');
		const hostname = url.searchParams.get('hubhost') || url.hostname;
		const hostTop = hostname.split('.')[0];

		// 确定 hubHost
		const hubHost = ns ? (ns === 'docker.io' ? DEFAULT_HUB_HOST : ns) : routeByHosts(hostTop);

		console.log(`域名头部: ${hostTop}\n反代地址: ${hubHost}`);

		// 修改包含 %2F 和 %3A 的请求
		if (!/%2F/.test(url.search) && /%3A/.test(url.toString())) {
			url.search = url.search.replace(/%3A(?=.*?&)/, '%3Alibrary%2F');
			console.log(`handle_url: ${url}`);
		}

		// 处理token请求
		if (url.pathname.includes('/token')) {
			const tokenUrl = AUTH_URL + url.pathname + url.search;
			return fetch(new Request(tokenUrl, {
				...request,
				headers: new Headers({
					'Host': 'auth.docker.io',
					'User-Agent': request.headers.get("User-Agent") || '',
					'Accept': request.headers.get("Accept") || '',
					'Accept-Language': request.headers.get("Accept-Language") || '',
					'Accept-Encoding': request.headers.get("Accept-Encoding") || '',
					'Connection': 'keep-alive',
					'Cache-Control': 'max-age=0'
				})
			}));
		}

		// 修改 /v2/ 请求路径
		if (hubHost === DEFAULT_HUB_HOST && /^\/v2\/[^/]+\/[^/]+\/[^/]+$/.test(url.pathname) && !/^\/v2\/library/.test(url.pathname)) {
			url.pathname = '/v2/library/' + url.pathname.split('/v2/')[1];
			console.log(`modified_url: ${url.pathname}`);
		}

		// 更改请求的主机名
		url.hostname = hubHost;

		// 构造请求参数
		const headers = new Headers(request.headers);
		headers.set('Host', hubHost);

		// 发起请求并处理响应
		const originalResponse = await fetch(new Request(url, { ...request, headers }));
		const newHeaders = new Headers(originalResponse.headers);

		// 修改 Www-Authenticate 头
		const wwwAuthenticate = newHeaders.get("Www-Authenticate");
		if (wwwAuthenticate) {
			newHeaders.set("Www-Authenticate", wwwAuthenticate.replace(new RegExp(AUTH_URL, 'g'), workersUrl));
		}

		// 处理重定向
		const location = newHeaders.get("Location");
		if (location) {
			return httpHandler(request, location);
		}

		// 返回修改后的响应
		return new Response(originalResponse.body, {
			status: originalResponse.status,
			headers: newHeaders
		});
	}
};

/**
 * 处理HTTP请求
 * @param {Request} req 请求对象
 * @param {string} pathname 请求路径
 * @returns {Promise<Response>} 返回处理后的响应
 */
async function httpHandler(req: Request, pathname: string): Promise<Response> {
	if (req.method === 'OPTIONS' && req.headers.has('access-control-request-headers')) {
		return new Response(null, PREFLIGHT_INIT);
	}

	const url = new URL(pathname);
	return proxy(url, req);
}

/**
 * 代理请求
 * @param {URL} urlObj URL对象
 * @param {Request} req 请求对象
 * @returns {Promise<Response>} 返回代理后的响应
 */
async function proxy(urlObj: URL, req: Request): Promise<Response> {
	const res = await fetch(urlObj.href, req);
	const headers = new Headers(res.headers);

	headers.set('access-control-expose-headers', '*');
	headers.set('access-control-allow-origin', '*');
	headers.set('Cache-Control', 'max-age=1500');

	// 删除不必要的头
	headers.delete('content-security-policy');
	headers.delete('content-security-policy-report-only');
	headers.delete('clear-site-data');

	return new Response(res.body, {
		status: res.status,
		headers: headers
	});
}
