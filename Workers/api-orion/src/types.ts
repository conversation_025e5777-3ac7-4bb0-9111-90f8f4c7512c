// 环境变量接口
export interface Env {
	DB: D1Database;                  // D1 数据库实例
	KV: KVNamespace;				 // KV 数据库实例
	ALLOWED_UA: string;              // 允许的 User-Agent 字符串
	ENABLE_LOGS: string;             // 是否启用日志记录的标志
}

// 许可证请求接口
export interface LicenseRequest {
	mac_address: string;             // 客户端 MAC 地址
	client_ip: string;               // 客户端 IPv4 地址
	license: string;                 // 加密的许可证字符串
}

// 许可证数据接口 (查询数据库相关字段的值)
export interface LicenseData {
	id: string;                      // 许可证唯一标识符
	user_id: string;                 // 用户 ID
	mac_address: string | null;      // MAC 地址, 允许为 null
	expiry_date: string;             // 许可证过期日期
	encrypted_license: string;       // 加密的许可证字符串
	authorized_count: number;        // 授权使用次数
	used_count: number;              // 已使用次数
}

// 许可证计数接口
export interface LicenseCount {
	authorized: number;              // 授权使用次数
	used: number;                    // 已使用次数
}

// 成功响应接口
export interface SuccessResponse {
	license_info: {
		user_id: string;             // 用户 ID
		mac_address: string | null;  // MAC 地址, 允许为 null
		expiry_date: string;         // 许可证过期日期
	};
	license_count: LicenseCount;     // 许可证使用计数
	license: string;                 // 加密的许可证字符串
}

// 错误响应接口
export interface ErrorResponse {
	code: string;                    // 错误代码
	msg: string;                     // 错误消息
}
