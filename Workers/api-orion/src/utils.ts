import { ErrorResponse, Env } from './types';

// 控制日志是否在 console 打印
export function log(env: Env, message: string, data?: any): void {
	if (env.ENABLE_LOGS === 'true') {
		const logEntry = JSON.stringify({
			message,
			timestamp: new Date().toISOString(),
			...data
		});
		console.log(logEntry);
	}
}

// 验证 UUID 格式
export function isValidUUID(uuid: string): boolean {
	const regex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
	return regex.test(uuid);
}

// 验证 MAC 地址格式
export function isValidMACAddress(mac: string): boolean {
	const regex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
	return regex.test(mac.toLowerCase());
}

// 标准化 MAC 地址
export function normalizeMACAddress(mac: string): string {
	return mac.toLowerCase();
}

// 验证 Base64 编码
export function isBase64(env: Env, str: string): boolean {
	// 检查字符串是否只包含有效的 Base64 字符（包括 URL 安全变体和可能的填充）
	const base64Regex = /^[-A-Za-z0-9+/]*={0,3}$/;

	if (!base64Regex.test(str)) {
		log(env, 'Base64 regex test failed');
		return false;
	}

	// 检查字符串长度是否在合理范围内
	if (str.length < 1 || str.length > 1000) {
		log(env, 'Base64 string length out of reasonable range', { length: str.length });
		return false;
	}

	return true;
}

// 标准化日期格式
export function standardizeDate(dateString: string): string {
	// 移除毫秒部分
	let standardizedDate = dateString.split('.')[0];
	// 确保以 'Z' 结尾
	if (!standardizedDate.endsWith('Z')) {
		standardizedDate += 'Z';
	}
	return standardizedDate;
}

// 校验 IPv4 格式
export function isValidIPv4(ip: string): boolean {
	const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
	return ipv4Regex.test(ip);
}

// 比较两个日期字符串
export function compareDates(date1: string, date2: string): number {
	const d1 = new Date(date1);
	const d2 = new Date(date2);
	return d1.getTime() - d2.getTime();
}

// 正则匹配 User-Agent
export function matchUserAgent(userAgent: string, pattern: string): boolean {
	// 将模式分割成前缀和后缀
	const [prefix, suffix] = pattern.split('*');

	// 创建正则表达式
	const regex = new RegExp('^' + prefix + '[0-9]+(\\.[0-9]+)*' + suffix + '$');

	return regex.test(userAgent);
}

// 创建错误响应
export function createErrorResponse(code: string, msg: string): Response {
	const errorResponse: ErrorResponse = { code, msg };
	return new Response(JSON.stringify(errorResponse), {
		status: parseInt(code),
		headers: { 'Content-Type': 'application/json' },
	});
}
