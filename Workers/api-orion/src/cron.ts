import { Env } from './types';
import { log, standardizeDate, compareDates } from './utils';
import {
	getAllLicenseIds,
	getLastRequestDates,
	updateUsedCounts,
	getAllUniqueMacAddresses,
	cleanupExpiredRecordsBatch
} from './db';
import { getActiveMacAddresses, removeActiveMacAddressesBatch, clearActiveMacAddressesBatch, updateActiveMacAddressesBatch } from './kv';

// 定义清理阈值（小时）
const CLEANUP_THRESHOLD_HOURS = 48;

// 导出 handleCron 函数，使其可以被其他模块使用
export async function handleCron(env: Env): Promise<void> {
	try {
		log(env, 'Starting cron job');

		// 获取所有许可证 ID
		const licenseIds = await getAllLicenseIds(env.DB);

		// 批量处理所有许可证
		await processLicenses(env, licenseIds);

		log(env, 'Cron job completed successfully');
	} catch (error) {
		log(env, 'Error in cron job', { error: error instanceof Error ? error.message : 'Unknown error' });
	}
}

async function processLicenses(env: Env, licenseIds: string[]): Promise<void> {
	const currentTime = new Date();
	const thresholdTime = new Date(currentTime.getTime() - CLEANUP_THRESHOLD_HOURS * 60 * 60 * 1000);
	const thresholdDate = standardizeDate(thresholdTime.toISOString());

	// 批量获取所有许可证的活跃 MAC 地址
	const allActiveMacAddresses = await Promise.all(licenseIds.map(id => getActiveMacAddresses(env, id)));

	// 批量获取所有许可证的最后请求日期
	const allLastRequestDates = await getLastRequestDates(env.DB, licenseIds);

	const inactiveMacAddresses: { [key: string]: string[] } = {};
	const licenseIdsToUpdate: string[] = [];

	licenseIds.forEach((licenseId, index) => {
		const activeMacAddresses = allActiveMacAddresses[index];
		const lastRequestDates = allLastRequestDates[licenseId] || {};

		inactiveMacAddresses[licenseId] = activeMacAddresses.filter(macAddress => {
			const lastRequestDate = lastRequestDates[macAddress];
			return lastRequestDate && compareDates(lastRequestDate, thresholdDate) < 0;
		});

		if (inactiveMacAddresses[licenseId].length > 0) {
			licenseIdsToUpdate.push(licenseId);
		}
	});

	// 批量移除不活跃的 MAC 地址
	await removeActiveMacAddressesBatch(env, licenseIdsToUpdate.map(licenseId => ({
		licenseId,
		macAddresses: inactiveMacAddresses[licenseId]
	})));

	// 批量清理过期记录
	await cleanupExpiredRecordsBatch(env.DB, licenseIdsToUpdate, thresholdDate);

	// 批量同步 KV 存储和数据库
	await syncKVAndDatabaseBatch(env, licenseIdsToUpdate);

	// 批量更新 used_count
	await updateUsedCounts(env, licenseIdsToUpdate);

	// 清理空的活跃 MAC 地址列表
	const emptyLicenseIds = licenseIdsToUpdate.filter(licenseId => inactiveMacAddresses[licenseId].length === allActiveMacAddresses[licenseIds.indexOf(licenseId)].length);
	await clearActiveMacAddressesBatch(env, emptyLicenseIds);

	log(env, 'Licenses processed', { processedCount: licenseIdsToUpdate.length, totalCount: licenseIds.length });
}

async function syncKVAndDatabaseBatch(env: Env, licenseIds: string[]): Promise<void> {
	const kvMacAddresses = await Promise.all(licenseIds.map(id => getActiveMacAddresses(env, id)));
	const dbMacAddresses = await Promise.all(licenseIds.map(id => getAllUniqueMacAddresses(env.DB, id)));

	const updates = licenseIds.map((licenseId, index) => ({
		licenseId,
		macAddresses: [...new Set([...kvMacAddresses[index], ...dbMacAddresses[index]])]
	}));

	await updateActiveMacAddressesBatch(env, updates);

	log(env, 'Synced KV and database batch', { licenseCount: licenseIds.length });
}
