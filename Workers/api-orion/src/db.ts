import { LicenseData, Env } from './types';
import { updateActiveMacAddressesBatch } from './kv';
import { log } from './utils';

// 根据许可证 ID 查询许可证信息
export async function getLicenseById(db: D1Database, id: string): Promise<LicenseData | null> {
	const query = `
    SELECT id, user_id, mac_address, expiry_date, encrypted_license, authorized_count, used_count
    FROM licenses
    WHERE id = ?
    `;
	const result = await db.prepare(query).bind(id).first();
	return result as LicenseData | null;
}

// 记录每次请求的日志
export async function logLicenseRequest(db: D1Database, licenseId: string, macAddress: string, clientIp: string, requestDate: string): Promise<void> {
	const query = `
    INSERT INTO license_requests (license_id, mac_address, client_ip, request_date)
    VALUES (?, ?, ?, ?)
    `;
	await db.prepare(query).bind(licenseId, macAddress, clientIp, requestDate).run();
}

// 获取所有唯一的 MAC 地址
export async function getAllUniqueMacAddresses(db: D1Database, licenseId: string): Promise<string[]> {
	const query = `
    SELECT DISTINCT mac_address
    FROM license_requests
    WHERE license_id = ?
    `;
	const result = await db.prepare(query).bind(licenseId).all();
	return (result?.results as Array<{ mac_address: string }> || []).map(row => row.mac_address);
}

// 更新 used_count 并同步 KV 存储
export async function updateUsedCount(env: Env, licenseId: string): Promise<void> {
	const macAddresses = await getAllUniqueMacAddresses(env.DB, licenseId);

	// 更新 KV 存储
	await updateActiveMacAddressesBatch(env, [{ licenseId, macAddresses }]);

	// 更新数据库中的 used_count
	const updateQuery = `
    UPDATE licenses
    SET used_count = ?
    WHERE id = ?
    `;
	await env.DB.prepare(updateQuery).bind(macAddresses.length, licenseId).run();

	log(env, 'Updated used count and KV storage', { licenseId, newCount: macAddresses.length });
}

// 获取唯一的 mac_address 数量
export async function getUniqueMacAddressCount(db: D1Database, licenseId: string): Promise<number> {
	const query = `
    SELECT COUNT(DISTINCT mac_address) as unique_count
    FROM license_requests
    WHERE license_id = ?
    `;
	const result = await db.prepare(query).bind(licenseId).first();
	return result ? (result.unique_count as number) : 0;
}

// 批量清理过期记录
export async function cleanupExpiredRecordsBatch(db: D1Database, licenseIds: string[], thresholdDate: string): Promise<void> {
	const query = `
    DELETE FROM license_requests
    WHERE license_id IN (${licenseIds.map(() => '?').join(',')}) AND request_date < ?
    `;
	await db.prepare(query).bind(...licenseIds, thresholdDate).run();
}

// 获取所有许可证 ID
export async function getAllLicenseIds(db: D1Database): Promise<string[]> {
	const query = `
    SELECT id
    FROM licenses
    `;
	const results = await db.prepare(query).all();
	return (results?.results as Array<{ id: string }> || []).map(row => row.id);
}

// 批量获取最后请求日期
export async function getLastRequestDates(db: D1Database, licenseIds: string[]): Promise<{ [key: string]: { [macAddress: string]: string } }> {
	const query = `
    SELECT license_id, mac_address, request_date
    FROM license_requests
    WHERE license_id IN (${licenseIds.map(() => '?').join(',')})
    ORDER BY request_date DESC
    `;
	const results = await db.prepare(query).bind(...licenseIds).all();
	const lastRequestDates: { [key: string]: { [macAddress: string]: string } } = {};
	(results?.results as Array<{ license_id: string, mac_address: string, request_date: string }> || []).forEach(row => {
		if (!lastRequestDates[row.license_id]) {
			lastRequestDates[row.license_id] = {};
		}
		if (!lastRequestDates[row.license_id][row.mac_address]) {
			lastRequestDates[row.license_id][row.mac_address] = row.request_date;
		}
	});
	return lastRequestDates;
}

// 批量更新 used_count
export async function updateUsedCounts(env: Env, licenseIds: string[]): Promise<void> {
	const updatePromises = licenseIds.map(async licenseId => {
		const macAddresses = await getAllUniqueMacAddresses(env.DB, licenseId);
		return { licenseId, macAddresses };
	});

	const updates = await Promise.all(updatePromises);

	// 批量更新 KV 存储
	await updateActiveMacAddressesBatch(env, updates);

	// 更新数据库中的 used_count
	const updateQuery = `
    UPDATE licenses
    SET used_count = ?
    WHERE id = ?
    `;

	await Promise.all(updates.map(({ licenseId, macAddresses }) =>
		env.DB.prepare(updateQuery).bind(macAddresses.length, licenseId).run()
	));

	updates.forEach(({ licenseId, macAddresses }) => {
		log(env, 'Updated used count and KV storage', { licenseId, newCount: macAddresses.length });
	});
}
