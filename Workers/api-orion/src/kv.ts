import { Env } from './types';
import { log } from './utils';

// 获取活跃的 mac_address 列表
export async function getActiveMacAddresses(env: Env, licenseId: string): Promise<string[]> {
	const kvKey = `${licenseId}`;
	try {
		const macAddresses = await env.KV.get(kvKey, 'json');
		return macAddresses ? (macAddresses as string[]) : [];
	} catch (error) {
		log(env, 'Failed to get active mac addresses from KV', { licenseId, error: error instanceof Error ? error.message : 'Unknown error' });
		return [];
	}
}

// 批量更新活跃的 mac_address 列表
export async function updateActiveMacAddressesBatch(env: Env, updates: { licenseId: string, macAddresses: string[] }[]): Promise<void> {
	try {
		const kvPromises = updates.map(({ licenseId, macAddresses }) =>
			env.KV.put(`${licenseId}`, JSON.stringify(macAddresses))
		);
		await Promise.all(kvPromises);
		log(env, 'Updated active MAC addresses in KV batch', { count: updates.length });
	} catch (error) {
		log(env, 'Failed to update active MAC addresses in KV batch', { error: error instanceof Error ? error.message : 'Unknown error' });
	}
}

// 添加一个新的 mac_address 到活跃列表
export async function addActiveMacAddress(env: Env, licenseId: string, macAddress: string): Promise<void> {
	const kvKey = `${licenseId}`;
	try {
		const macAddresses = await getActiveMacAddresses(env, licenseId);
		if (!macAddresses.includes(macAddress)) {
			macAddresses.push(macAddress);
			await env.KV.put(kvKey, JSON.stringify(macAddresses));
			log(env, 'Added new MAC address to active list', { licenseId, macAddress });
		}
	} catch (error) {
		log(env, 'Failed to add active mac address to KV', { licenseId, macAddress, error: error instanceof Error ? error.message : 'Unknown error' });
	}
}

// 批量从活跃列表中删除 mac_address
export async function removeActiveMacAddressesBatch(env: Env, updates: { licenseId: string, macAddresses: string[] }[]): Promise<void> {
	try {
		const kvPromises = updates.map(async ({ licenseId, macAddresses }) => {
			const currentMacAddresses = await getActiveMacAddresses(env, licenseId);
			const updatedMacAddresses = currentMacAddresses.filter(addr => !macAddresses.includes(addr));
			return env.KV.put(`${licenseId}`, JSON.stringify(updatedMacAddresses));
		});
		await Promise.all(kvPromises);
		log(env, 'Removed MAC addresses from active lists in batch', { count: updates.length });
	} catch (error) {
		log(env, 'Failed to remove active mac addresses from KV in batch', { error: error instanceof Error ? error.message : 'Unknown error' });
	}
}

// 批量清空活跃的 mac_address 列表
export async function clearActiveMacAddressesBatch(env: Env, licenseIds: string[]): Promise<void> {
	try {
		const kvPromises = licenseIds.map(licenseId => env.KV.delete(`${licenseId}`));
		await Promise.all(kvPromises);
		log(env, 'Cleared active MAC addresses in KV batch', { count: licenseIds.length });
	} catch (error) {
		log(env, 'Failed to clear active mac addresses in KV batch', { error: error instanceof Error ? error.message : 'Unknown error' });
	}
}
