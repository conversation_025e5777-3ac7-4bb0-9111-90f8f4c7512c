import { Env, LicenseRequest, SuccessResponse } from './types';
import {
	isValidUUID,
	isValidMACAddress,
	isBase64,
	createErrorResponse,
	log,
	normalizeMACAddress,
	isValidIPv4,
	standardizeDate,
	matchUserAgent
} from './utils';
import { getLicenseById, logLicenseRequest, updateUsedCount } from './db';
import { getActiveMacAddresses, addActiveMacAddress } from './kv';
import { handleCron } from './cron';

// 计算 JSON 字符串中的键数量
function countKeys(jsonString: string): number {
	const keyRegex = /"(?:\\.|[^"\\])*"\s*:/g;
	const matches = jsonString.match(keyRegex);
	return matches ? matches.length : 0;
}

export default {
	async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
		const url = new URL(request.url);

		// 检查路由
		if (url.pathname !== '/license/check') {
			return createErrorResponse('403', 'Unauthorized Access');
		}

		// 检查请求方法
		if (request.method !== 'POST') {
			return createErrorResponse('403', 'Unauthorized Access');
		}

		// 验证 User-Agent 请求头
		const userAgent = request.headers.get('User-Agent');
		const allowedUA = env.ALLOWED_UA;

		if (!userAgent || !matchUserAgent(userAgent, allowedUA)) {
			log(env, 'Invalid User-Agent', { userAgent });
			return createErrorResponse('403', 'Unauthorized Access');
		}

		// 验证 Authorization 请求头
		const authHeader = request.headers.get('Authorization');
		if (!authHeader || !authHeader.startsWith('Bearer ') || !isValidUUID(authHeader.split(' ')[1])) {
			log(env, 'Invalid Authorization header', { authHeader });
			return createErrorResponse('403', 'Unauthorized Access');
		}

		const licenseId = authHeader.split(' ')[1];

		try {
			// 验证请求体是否为空
			const requestBodyText = await request.text();
			if (!requestBodyText) {
				log(env, 'Empty request body');
				return createErrorResponse('403', 'Unauthorized Access');
			}

			const originalKeyCount = countKeys(requestBodyText);
			let rawRequestBody;

			try {
				rawRequestBody = JSON.parse(requestBodyText);
			} catch (parseError) {
				log(env, 'Invalid JSON in request body', { error: parseError instanceof Error ? parseError.message : 'Unknown error' });
				return createErrorResponse('403', 'Unauthorized Access');
			}

			const parsedKeyCount = Object.keys(rawRequestBody).length;
			if (originalKeyCount !== parsedKeyCount) {
				log(env, 'Potential duplicate keys in request body', {
					originalCount: originalKeyCount,
					parsedCount: parsedKeyCount
				});
				return createErrorResponse('403', 'Unauthorized Access');
			}

			// 类型断言，确保请求体符合 LicenseRequest 接口
			const requestBody = rawRequestBody as LicenseRequest;

			// 验证请求体的必要字段
			if (!requestBody.mac_address || !requestBody.license || !requestBody.client_ip) {
				log(env, 'Missing required fields in request body');
				return createErrorResponse('403', 'Unauthorized Access');
			}

			// 验证请求体 MAC 地址格式是否合法
			if (!isValidMACAddress(requestBody.mac_address)) {
				log(env, 'Invalid MAC address in request body', {
					macAddress: requestBody.mac_address
				});
				return createErrorResponse('403', 'Unauthorized Access');
			}

			// 验证请求体 license 是否为 Base64 编码
			if (!isBase64(env, requestBody.license)) {
				log(env, 'Invalid license in request body', {
					licenseLength: requestBody.license.length
				});
				return createErrorResponse('403', 'Unauthorized Access');
			}

			const normalizedRequestMac = normalizeMACAddress(requestBody.mac_address);

			// 验证请求体 client_ip 字段的值是否为 IPv4 格式, 若不是则替换值, 但不拦截请求
			let clientIp = requestBody.client_ip || '';
			if (!clientIp || !isValidIPv4(clientIp)) {
				clientIp = 'Invalid';
			}

			// 查询数据库
			const licenseData = await getLicenseById(env.DB, licenseId);

			if (!licenseData) {
				log(env, 'License ID not found', { licenseId });
				return createErrorResponse('403', 'Unauthorized');
			}

			// 验证请求体 MAC 地址是否与数据库一致
			if (licenseData.mac_address !== null) {
				const normalizedDbMac = normalizeMACAddress(licenseData.mac_address);
				if (normalizedDbMac !== normalizedRequestMac) {
					log(env, 'MAC address mismatch', {
						requestMac: normalizedRequestMac,
						dbMac: normalizedDbMac
					});
					return createErrorResponse('403', 'Unauthorized MAC Address');
				}
			}

			// 验证请求体加密许可证是否与数据库一致
			if (licenseData.encrypted_license !== requestBody.license) {
				log(env, 'License mismatch', {
					requestLicense: requestBody.license,
					dbLicense: licenseData.encrypted_license
				});
				return createErrorResponse('403', 'Unauthorized License');
			}

			log(env, 'Starting license request processing', { licenseId, macAddress: normalizedRequestMac });

			// 异步记录请求日志
			const requestDate = standardizeDate(new Date().toISOString());
			ctx.waitUntil(logLicenseRequest(env.DB, licenseId, normalizedRequestMac, clientIp, requestDate));

			// 检查 MAC 地址是否已存在于活跃列表中
			const activeMacAddresses = await getActiveMacAddresses(env, licenseId);
			if (!activeMacAddresses.includes(normalizedRequestMac)) {
				// MAC 地址不存在，先添加到 KV 存储
				await addActiveMacAddress(env, licenseId, normalizedRequestMac);

				// 然后更新 used_count
				await updateUsedCount(env, licenseId);

				log(env, 'New MAC address added and used count updated', { licenseId, macAddress: normalizedRequestMac });
			}

			// 获取更新后的许可证数据
			const updatedLicenseData = await getLicenseById(env.DB, licenseId);

			if (!updatedLicenseData) {
				log(env, 'Updated license data not found', { licenseId });
				return createErrorResponse('500', 'Internal Server Error');
			}

			log(env, 'License request processed', { licenseId, macAddress: normalizedRequestMac, updatedUsedCount: updatedLicenseData.used_count });

			// 返回许可证信息
			const response: SuccessResponse = {
				license_info: {
					user_id: updatedLicenseData.user_id,
					mac_address: updatedLicenseData.mac_address || null,
					expiry_date: updatedLicenseData.expiry_date,
				},
				license_count: {
					authorized: updatedLicenseData.authorized_count,
					used: updatedLicenseData.used_count,
				},
				license: updatedLicenseData.encrypted_license,
			};

			return new Response(JSON.stringify(response), {
				status: 200,
				headers: { 'Content-Type': 'application/json' },
			});

		} catch (error: unknown) {
			// 记录错误日志
			if (error instanceof Error) {
				log(env, 'Error occurred', {
					error: error.message,
					stack: error.stack
				});
			} else {
				log(env, 'Unknown error occurred', { error });
			}
			return createErrorResponse('500', 'Internal Server Error');
		}
	},

	async scheduled(_event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
		// 调用 cron.ts 中定义的 handleCron 函数
		ctx.waitUntil(handleCron(env));
	}
};
