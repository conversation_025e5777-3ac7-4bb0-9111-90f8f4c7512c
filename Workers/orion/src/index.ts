import { Env } from './types';
import { handleRequest } from './handlers';

// 导出默认对象，包含 Cloudflare Worker 的主要处理函数
export default {
	// fetch 函数是 Cloudflare Worker 的入口点
	// 它接收所有传入的 HTTP 请求，并返回响应
	async fetch(request: Request, env: Env): Promise<Response> {
		try {
			// 将请求和环境变量传递给 handleRequest 函数处理
			// handleRequest 函数定义在 handlers.ts 中，负责具体的请求处理逻辑
			// 包括路由分发、许可证的创建、读取、更新和删除操作
			return await handleRequest(request, env);
		} catch (error) {
			// 捕获并处理任何未预期的错误
			console.error('Unexpected error in fetch:', error);
			return new Response('An unexpected error occurred', { status: 500 });
		}
	},
};
