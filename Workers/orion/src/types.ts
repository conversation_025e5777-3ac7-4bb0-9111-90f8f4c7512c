// 定义环境变量接口
export interface Env {
	// DB: D1 数据库实例
	DB: D1Database;
}

// 定义用户接口
export interface User {
	// 用户唯一标识符
	id: string;

	// 用户名
	username: string;

	// 创建日期 (ISO 8601 格式的字符串)
	created_at: string;

	// 许可证 (可选字段)
	licenses?: License[];
}

// 定义许可证接口
export interface License {
	// 许可证唯一标识符
	id: string;

	// 关联的用户ID
	user_id: string;

	// MAC 地址 (可选字段)
	mac_address?: string;

	// 过期日期 (ISO 8601 格式的字符串)
	expiry_date: string;

	// 创建日期 (ISO 8601 格式的字符串)
	created_at: string;

	// 加密后的许可证内容
	encrypted_license: string;

	// 授权数量
	authorized_count: number;

	// 已授权数量
	used_count: number;

	// 备注 (可选字段)
	note?: string;
}

// 定义API响应的许可证数据接口
export interface LicenseResponse {
	// 许可证信息
	license_info: {
		// 许可证唯一标识符
		id: string;

		// 关联的用户ID
		user_id: string;

		// 用户名
		username: string;

		// MAC 地址
		mac_address: string;

		// 过期日期 (ISO 8601 格式的字符串)
		expiry_date: string;

		// 创建日期 (ISO 8601 格式的字符串)
		created_at: string;
	};

	// 加密后的许可证内容
	encrypted_license: string;
}

// 定义许可证请求日志接口
export interface LicenseRequestLog {
	// MAC 地址
	mac_address: string;

	// 客户端 IP
	client_ip: string;

	// 请求日期 (ISO 8601 格式的字符串)
	request_date: string;
}
