body {
	background-color: #F4F5F7;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.navbar {
	background-color: #FFFFFF;
	box-shadow: 0 1px 0 rgba(9, 30, 66, 0.13);
}

.navbar-brand {
	color: #0052CC;
	font-weight: 500;
}

.navbar-nav {
	position: relative;
}

.nav-link {
	color: #42526E;
	transition: color 0.3s ease;
	z-index: 2;
}

.nav-link.active {
	font-weight: 600;
	color: #0052CC !important;
}

.nav-background {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	background-color: #DEEBFF;
	transition: all 0.3s ease;
	z-index: 1;
	border-radius: 3px;
}

.navbar .container-fluid {
	display: flex;
	justify-content: space-between;
	padding: 10px 30px;
}

.container {
	max-width: 1400px;
	margin-top: 2rem;
}

.card {
	border: none;
	border-radius: 3px;
	box-shadow: 0 1px 1px rgba(9, 30, 66, 0.25), 0 0 1px 0 rgba(9, 30, 66, 0.31);
	background-color: #FFFFFF;
	transition: box-shadow 0.3s ease;
}

.card:hover {
	box-shadow: 0 4px 8px rgba(9, 30, 66, 0.25), 0 0 1px 0 rgba(9, 30, 66, 0.31);
}

.card-header {
	background-color: #F4F5F7;
	border-bottom: 1px solid #DFE1E6;
	padding: 12px 20px;
	font-size: 14px;
	font-weight: 600;
	color: #172B4D;
}

.btn-primary {
	background-color: #0052CC;
	border-color: #0052CC;
	transition: background-color 0.2s ease, border-color 0.2s ease;
}

.btn-primary:hover,
.btn-primary:focus {
	background-color: #0065FF;
	border-color: #0065FF;
}

.btn-primary:active {
	background-color: #0747A6;
	border-color: #0747A6;
}

.table th {
	font-weight: 600;
	color: #5E6C84;
	font-size: 12px;
	text-transform: uppercase;
}

.table td {
	color: #172B4D;
}

.nav-link {
	color: #42526E;
	transition: background-color 0.3s ease;
}

.nav-link.active {
	font-weight: 600;
	color: #0052CC !important;
	background-color: #DEEBFF;
	border-radius: 3px;
}

#generateSection,
#listSection {
	display: none;
	opacity: 0;
	transition: opacity 0.3s ease;
}

#generateSection.active,
#listSection.active {
	display: block;
	opacity: 1;
}

#updateLicenseModal .modal-header {
	background-color: #F4F5F7;
	border-bottom: 1px solid #DFE1E6;
}

#updateLicenseModal .modal-title {
	font-size: 1.25rem;
	color: #000;
}

#updateLicenseModal .modal-body {
	padding-top: 1.5rem;
}

#updateLicenseModal .btn-close {
	font-size: 0.8rem;
}

#updateLicenseModal .modal-footer {
	padding: 1rem;
}

#updateLicenseModal .modal-footer .btn {
	margin-right: 0.5rem;
}

#updateLicenseModal .modal-footer .btn:last-child {
	margin-right: 0;
}

.btn-secondary {
	background-color: #F4F5F7;
	color: #42526E;
	border-color: #DFE1E6;
}

.btn-secondary:hover {
	background-color: #EBECF0;
	color: #172B4D;
}

#usernameDropdown {
	position: absolute;
	width: 100%;
	z-index: 1000;
	max-height: 200px;
	overflow-y: auto;
	display: none;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	border: 1px solid #DFE1E6;
	border-radius: 0 0 3px 3px;
	background-color: #FFFFFF;
}

#usernameDropdown.show {
	display: block;
}

#usernameDropdown .list-group-item {
	cursor: pointer;
	transition: background-color 0.2s ease;
}

#usernameDropdown .list-group-item:hover {
	background-color: #F4F5F7;
}

.form-control,
.form-select {
	border-radius: 3px;
	border: 2px solid #DFE1E6;
	transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	padding: 8px 12px;
}

.form-control:focus,
.form-select:focus {
	border-color: #4C9AFF;
	box-shadow: 0 0 0 2px rgba(76, 154, 255, 0.3);
}

.user-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background-color: #0052CC;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
}

.list-group-item {
	transition: background-color 0.2s ease;
	padding: 1rem;
}

.list-group-item:hover {
	background-color: #F4F5F7;
}

.list-group-item h6 {
	color: #172B4D;
	margin-bottom: 0.25rem;
}

.list-group-item small {
	color: #6B778C;
	display: block;
}

.user-meta {
	margin-top: 0.5rem;
	display: flex;
	align-items: center;
}

.user-meta span {
	margin-right: 1rem;
	font-size: 0.875rem;
}

.license-count {
	cursor: pointer;
	background-color: #0C66E4;
	color: #FFFFFF;
	padding: 0.25rem 0.5rem;
	font-size: 0.875rem;
	border-radius: 0.25rem;
	font-weight: 600;
	transition: background-color 0.2s ease;
	position: relative;
}

.license-count:hover {
	background-color: #0065FF;
}

.license-count .ping-wrapper {
	position: absolute;
	top: -4px;
	right: -4px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 12px;
	height: 12px;
}

.license-count .animate-ping {
	position: absolute;
	height: 100%;
	width: 100%;
	border-radius: 50%;
	background-color: #22A06B;
	opacity: 0.75;
	animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.license-count .ping-core {
	position: relative;
	height: 10px;
	width: 10px;
	border-radius: 50%;
	background-color: #22A06B;
}

@keyframes ping {
	75%, 100% {
		transform: scale(2);
		opacity: 0;
	}
}

.badge {
	font-size: 12px;
	padding: 0.25em 0.5em;
}

.table-sm th,
.table-sm td {
	padding: 0.3rem;
}

.btn-sm {
	padding: 0.25rem 0.5rem;
	font-size: 0.875rem;
}

.modal-dialog.modal-lg {
	max-width: 90%;
}

.license-table {
	font-size: 0.9rem;
}

.license-table th {
	background-color: #F4F5F7;
	font-weight: 600;
}

.license-table .encrypted-license {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	cursor: pointer;
}

.license-table .encrypted-license:hover {
	text-decoration: underline;
}

/* 许可证列表模态框样式 */
.batch-modal-fullscreen {
	display: none;
	position: fixed;
	z-index: 1050;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: auto;
	background-color: rgba(0, 0, 0, 0.4);
}

.batch-modal-content {
	background-color: #fefefe;
	margin: 5% auto;
	padding: 20px 30px;
	border: 1px solid #DFE1E6;
	width: 90%;
	max-width: 1200px;
	border-radius: 3px;
	box-shadow: 0 5px 10px rgba(9, 30, 66, 0.2);
}

.batch-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	border-bottom: none;
}

.batch-modal-query-count {
	font-size: 20px;
	font-weight: 500;
	color: #172B4D;
}

.batch-modal-close-button {
	color: #6B778C;
	font-size: 24px;
	font-weight: normal;
	cursor: pointer;
	background: none;
	border: none;
	padding: 0;
	line-height: 1;
}

.batch-modal-close-button:hover {
	color: #172B4D;
}

.batch-modal-result-list {
	display: flex;
	flex-direction: column;
}

.batch-modal-result-item {
	border-bottom: 1px solid #DFE1E6;
	padding: 25px 0;
}

.batch-modal-result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30px;
}

.batch-modal-ip-address {
	font-size: 18px;
	font-weight: 600;
	color: #172B4D;
}

.batch-modal-status {
	margin-left: 10px;
	font-size: 12px;
	font-weight: normal;
}

.batch-modal-result-details {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-top: 20px;
}

.detail-column {
	display: flex;
	flex-direction: column;
	flex: 1;
	margin-right: 20px;
}

.detail-column:last-child {
	margin-right: 0;
}

.detail-label {
	font-size: 12px;
	color: #6B778C;
	margin-bottom: 8px;
}

.detail-value {
	font-weight: 500;
	color: #44546F;
}

.batch-modal-as-info {
	font-size: 12px;
	color: #0052CC;
	cursor: pointer;
}

.batch-modal-as-details {
	margin-top: 20px;
}

.license-content {
	background-color: #EAE6FF;
	color: #403294;
	font-size: 13px;
	line-height: 1.5;
	padding: 10px;
	border-radius: 3px;
	margin-bottom: 10px;
}

.license-note {
	background-color: #CCE0FF;
	padding: 10px;
	border-radius: 3px;
	font-size: 13px;
}

.license-request-logs {
	background-color: #FFE5B4;
	padding: 10px;
	border-radius: 3px;
	margin-top: 10px;
	font-size: 13px;
}

.license-request-log-item {
	margin-bottom: 5px;
	font-size: 13px;
	line-height: 1.5;
	color: #172B4D;
}

.encrypted-license {
	word-break: break-all;
	color: #172B4D;
}

.bi-pencil-square,
.bi-trash {
	cursor: pointer;
	color: #6B778C;
	transition: color 0.2s ease;
}

.bi-pencil-square:hover,
.bi-trash:hover {
	color: #0052CC;
}

/* 新增点击复制鼠标悬停时出现下划线样式类 */
.clickable-item {
	position: relative;
	cursor: pointer;
	display: inline;
	background-image: linear-gradient(to right, #6B778C, #6B778C);
	background-size: 0% 2px;
	background-repeat: no-repeat;
	background-position: left bottom;
	transition: background-size 0.2s ease;
}

.clickable-item:hover {
	background-size: 100% 2px;
}

.dropdown-hover .btn-outline-secondary {
	height: 43px;
	line-height: 1.5;
	padding: 0.375rem 0.75rem;
	border-color: #CED4DA;
	background-color: #F1F2F4;
	color: #212529;
}

.dropdown-hover .btn-outline-secondary:hover,
.dropdown-hover .btn-outline-secondary:focus {
	background-color: #DCDFE4;
	border-color: #CED4DA;
	color: #212529;
}

.dropdown-hover .dropdown-menu {
	margin-top: 0;
	border-color: #ced4da;
	box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.row.g-2.align-items-center > .col-md-6 {
	display: flex;
	flex-direction: column;
	align-items: stretch;
}

.dropdown-hover {
	flex-grow: 1;
	display: flex;
}

.dropdown-hover .btn-outline-secondary {
	flex-grow: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	text-align: left;
}
