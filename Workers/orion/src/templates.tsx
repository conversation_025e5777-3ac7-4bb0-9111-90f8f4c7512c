export const HTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orion License Manager</title>
    <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/fastbootstrap/fastbootstrap.min.css?2.2.0"/>
    <link rel="stylesheet" href="https://tie3.lilh.net/pub/static/css/bootstrap/icons/bootstrap-icons.min.css?1.11.3"/>
    <link rel="stylesheet" href="https://tie3.lilh.net/priv/workers/orion/style.css"/>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-box-seam me-2"></i>
                Orion
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-background"></div>
                <a class="nav-link active px-3 py-2 me-2" href="#generateSection" id="generateLink">Generate License</a>
                <a class="nav-link px-3 py-2" href="#listSection" id="listLink">License List</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div id="generateSection" class="active">
            <div class="card mb-4">
                <div class="card-header">
                    Generate New License
                </div>
                <div class="card-body">
                    <form id="licenseForm" class="needs-validation" novalidate>
                        <div class="mb-3 position-relative">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required autocomplete="off" pattern="^[a-zA-Z0-9]+$">
                            <div class="invalid-feedback" id="usernameError">
                                Username must contain only letters and numbers.
                            </div>
                            <ul class="list-group" id="usernameDropdown"></ul>
                        </div>
                        <div class="mb-3">
                            <label for="macAddress" class="form-label">MAC Address</label>
                            <input type="text" class="form-control" id="macAddress" name="macAddress" pattern="^([0-9a-f]{2}:){5}[0-9a-f]{2}$">
                            <div class="invalid-feedback" id="macAddressFormatError">
                                Invalid MAC address format.
                            </div>
                            <div class="invalid-feedback" id="macAddressUniqueError" style="display: none;">
                                This MAC address is already in use.
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="expiryValue" class="form-label">Expiration</label>
                            <div class="row g-2 align-items-center">
                                <div class="col-md-6">
                                    <input type="number" class="form-control" id="expiryValue" name="expiryValue" required min="1">
                                    <div class="invalid-feedback" id="expiryValueError">
                                        Please enter a positive integer.
                                    </div>
                                    <div class="invalid-feedback" id="expiryDateError" style="display: none;">
                                        Please do not exceed 2099.12.31.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="dropdown dropdown-hover">
                                        <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" id="expiryUnitDropdown">
                                            <span id="selectedExpiryUnit">Days</span>
                                        </button>
                                        <ul class="dropdown-menu w-100" id="expiryUnitMenu">
                                            <li><a class="dropdown-item" href="#" data-value="days">Days</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="months">Months</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="years">Years</a></li>
                                            <li><a class="dropdown-item" href="#" data-value="permanent">Permanent</a></li>
                                        </ul>
                                    </div>
                                    <input type="hidden" id="expiryUnit" name="expiryUnit" value="days">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
							<label for="authorizedCount" class="form-label">Authorized Count</label>
							<input type="number" class="form-control" id="authorizedCount" name="authorizedCount" required min="1">
							<div class="invalid-feedback" id="authorizedCountError">
								Please enter a positive integer.
							</div>
						</div>
                        <div class="mb-3">
                            <label for="note" class="form-label">Note</label>
                            <textarea class="form-control" id="note" name="note" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Generate License</button>
                    </form>
                </div>
            </div>
        </div>
        <div id="listSection">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">User and License List</h5>
                    <div class="input-group" style="max-width: 300px;">
                        <input type="text" class="form-control" placeholder="Search users" id="userSearch">
                    </div>
                </div>
                <div class="card-body">
                    <div id="userList" class="list-group list-group-flush">
                        <!-- 用户列表将动态添加到这里 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户许可证列表模态框 -->
        <div class="batch-modal-fullscreen" id="licenseListModal">
            <div class="batch-modal-content">
                <div class="batch-modal-header">
                    <div id="licenseQueryCount" class="batch-modal-query-count"></div>
                    <button type="button" class="batch-modal-close-button" data-dismiss="modal">&times;</button>
                </div>
                <div class="batch-modal-result-list" id="licenseResultList"></div>
            </div>
        </div>

        <!-- 许可证信息模态框 -->
        <div class="modal fade" id="licenseListModal" tabindex="-1" aria-labelledby="licenseListModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="licenseListModalLabel">User Licenses</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="userLicenseList">
                            <!-- 用户的许可证列表将动态添加到这里 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更新许可证模态框 -->
		<div class="modal fade" id="updateLicenseModal" tabindex="-1" aria-labelledby="updateLicenseModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="updateLicenseModalLabel">Update License</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body p-6"> <!-- 增加内边距 -->
						<form id="updateLicenseForm" class="needs-validation" novalidate>
							<input type="hidden" id="updateLicenseId" name="id">
							<div class="mb-4"> <!-- 增加底部边距 -->
								<label for="updateMacAddress" class="form-label">MAC Address</label>
								<input type="text" class="form-control" id="updateMacAddress" name="macAddress" pattern="^([0-9a-f]{2}:){5}[0-9a-f]{2}$">
								<div class="invalid-feedback" id="updateMacAddressFormatError">
									Invalid MAC address format.
								</div>
								<div class="invalid-feedback" id="updateMacAddressUniqueError" style="display: none;">
									This MAC address is already in use.
								</div>
							</div>
							<div class="mb-4"> <!-- 增加底部边距 -->
								<label for="updateExpiryValue" class="form-label">Extend Expiration</label>
								<div class="row g-3 align-items-center"> <!-- 增加列间距 -->
									<div class="col-md-6">
										<input type="number" class="form-control" id="updateExpiryValue" name="expiryValue" min="1">
										<div class="invalid-feedback" id="updateExpiryValueError">
											Please enter a number.
										</div>
										<div class="invalid-feedback" id="updateExpiryDateError" style="display: none;">
											Please do not exceed 2099.12.31.
										</div>
									</div>
									<div class="col-md-6">
										<div class="dropdown dropdown-hover">
											<button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" id="updateExpiryUnitDropdown">
												<span id="updateSelectedExpiryUnit">Days</span>
											</button>
											<ul class="dropdown-menu w-100" id="updateExpiryUnitMenu">
												<li><a class="dropdown-item" href="#" data-value="days">Days</a></li>
												<li><a class="dropdown-item" href="#" data-value="months">Months</a></li>
												<li><a class="dropdown-item" href="#" data-value="years">Years</a></li>
												<li><a class="dropdown-item" href="#" data-value="permanent">Permanent</a></li>
											</ul>
										</div>
										<input type="hidden" id="updateExpiryUnit" name="expiryUnit" value="days">
									</div>
								</div>
							</div>
							<div class="mb-4"> <!-- 增加底部边距 -->
								<label for="updateAuthorizedCount" class="form-label">Authorized Count</label>
								<input type="number" class="form-control" id="updateAuthorizedCount" name="authorizedCount" min="1">
								<div class="invalid-feedback" id="updateAuthorizedCountError">
									Please enter a positive integer.
								</div>
							</div>
							<div class="mb-0"> <!-- 最后一个元素移除底部边距 -->
								<label for="updateNote" class="form-label">Note</label>
								<textarea class="form-control" id="updateNote" name="note" rows="3"></textarea>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-primary" id="updateLicenseButton">Update License</button>
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
					</div>
				</div>
			</div>
		</div>

    <script src="https://tie3.lilh.net/pub/static/js/bootstrap/bootstrap.bundle.min.js?5.3.3"></script>
    <script id="appScript"></script>
</body>
</html>
`;

export const clientScript = `
// 全局 showToast 函数
window.showToast = function(message, type) {
    const toastContainer = document.querySelector('.toast-container') || (() => {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    })();

    const toast = document.createElement('div');
    toast.className = \`toast align-items-center text-white bg-\${type} border-0\`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = \`
			<div class="d-flex">
					<div class="toast-body">\${message}</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
			</div>
	\`;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => toast.remove());
};

// 全局 deleteLicense 函数
window.deleteLicense = async function(id) {
    if (confirm('Are you sure you want to delete this license?')) {
        try {
            const response = await fetch(\`/licenses/\${id}\`, { method: 'DELETE' });
            if (!response.ok) throw new Error('Failed to delete license');
            window.showToast('License deleted successfully', 'success');
            window.loadLicenses();
            document.getElementById('licenseListModal').style.display = 'none'; // 关闭模态框
        } catch (error) {
            window.showToast(error.message, 'danger');
        }
    }
};

// 全局 formatDate 函数
window.formatDate = function(dateString) {

    // 移除多余的 Z
    dateString = dateString.replace(/Z+$/, 'Z');

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        console.error('无效日期:', dateString);
        return '无效日期';
    }

    const shanghaiDate = new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
    return shanghaiDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
    }).replace(/\\//g, '-');
};

document.addEventListener('DOMContentLoaded', function() {
		let users = [];
		let licenses = [];
		let selectedUsername = '';
		let originalValues = {};

		// 切换显示的部分
		function switchSection(showId, hideId) {
			const showElement = document.getElementById(showId);
			const hideElement = document.getElementById(hideId);

			if (showElement && hideElement) {
				hideElement.classList.remove('active');
				setTimeout(() => {
					hideElement.style.display = 'none';
					showElement.style.display = 'block';
					setTimeout(() => showElement.classList.add('active'), 50);
				}, 300);
			}
		}

		// 移动导航背景
		function moveNavBackground(target) {
			const navBackground = document.querySelector('.nav-background');
			if (navBackground && target) {
				navBackground.style.width = \`\${target.offsetWidth}px\`;
				navBackground.style.left = \`\${target.offsetLeft}px\`;
			}
		}

		// 导航链接点击事件
		document.querySelectorAll('.nav-link').forEach(link => {
			link.addEventListener('click', function(e) {
				e.preventDefault();
				const targetId = this.getAttribute('href').substring(1);
				const otherId = targetId === 'generateSection' ? 'listSection' : 'generateSection';
				switchSection(targetId, otherId);
				document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
				this.classList.add('active');
				moveNavBackground(this);
			});
		});

		// 页面加载时移动导航背景
		moveNavBackground(document.querySelector('.nav-link.active'));

		// 设置输入验证的通用函数
		function setupInputValidation(inputElement, validationFunction) {
			let isValidating = false;
			inputElement.addEventListener('input', debounce(async function(event) {
				if (isValidating) return;
				isValidating = true;
				const value = event.target.value;

				// 重置状态
				inputElement.classList.remove('is-invalid', 'is-valid');
				const errorElements = inputElement.parentElement.querySelectorAll('.invalid-feedback');
				errorElements.forEach(el => el.style.display = 'none');

				if (value) {
					const error = await validationFunction(value);
					if (error) {
						inputElement.setCustomValidity(error.message);
						inputElement.classList.add('is-invalid');
						const errorElement = inputElement.parentElement.querySelector(\`#\${error.id}\`);
						if (errorElement) {
							errorElement.style.display = 'block';
						}
					} else {
						inputElement.setCustomValidity('');
						inputElement.classList.add('is-valid');
					}
				} else {
					inputElement.setCustomValidity('');
				}

				inputElement.reportValidity();
				isValidating = false;
			}, 300));
		}

		/// 设置各个输入字段的验证
		// 验证用户名
		setupInputValidation(document.getElementById('username'), async (value) => {
			if (!value.match(/^[a-zA-Z0-9]+$/)) {
				return { id: 'usernameError', message: 'Username must contain only letters and numbers.' };
			}
			return null;
		});
		// 验证 MAC 地址
		setupInputValidation(document.getElementById('macAddress'), async (value) => {
			if (value === '') {
				return null; // 允许为空，不触发任何验证
			}
			if (!value.match(/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/i)) {
				return { id: 'macAddressFormatError', message: 'Invalid MAC address format.' };
			}
			try {
				const response = await fetch(\`/check_mac_address?mac=\${encodeURIComponent(value)}\`);
				const data = await response.json();
				if (data.exists) {
					return { id: 'macAddressUniqueError', message: 'This MAC address is already in use.' };
				}
			} catch (error) {
				console.error('Error checking MAC address:', error);
			}
			return null;
		});

		// 验证到期时间
		setupInputValidation(document.getElementById('expiryValue'), (value) => {
			const unit = document.getElementById('expiryUnit').value;
			if (isNaN(value) || value <= 0) {
				return { id: 'expiryValueError', message: 'Please enter a positive integer.' };
			}
			if (!validateExpiryDate(value, unit)) {
				return { id: 'expiryDateError', message: 'Please do not exceed 2099.12.31.' };
			}
			return null;
		});

		// 验证授权数量
		setupInputValidation(document.getElementById('authorizedCount'), (value) => {
			if (value === '') {
				return { id: 'authorizedCountError', message: 'Authorized Count is required.' };
			}
			if (!/^\\d+$/.test(value) || parseInt(value) <= 0) {
				return { id: 'authorizedCountError', message: 'Please enter a positive integer.' };
			}
			return null;
		});

		// 验证更新 MAC 地址
		setupInputValidation(document.getElementById('updateMacAddress'), async (value) => {
			const licenseId = document.getElementById('updateLicenseId').value;
			if (!value.match(/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/i)) {
				return { id: 'updateMacAddressFormatError', message: 'Invalid MAC address format.' };
			}
			try {
				const response = await fetch(\`/check_mac_address?mac=\${encodeURIComponent(value)}&exclude_id=\${licenseId}\`);
				const data = await response.json();
				if (data.exists) {
					return { id: 'updateMacAddressUniqueError', message: 'This MAC address is already in use.' };
				}
			} catch (error) {
				console.error('Error checking MAC address:', error);
			}
			return null;
		});

		// 验证更新到期时间
		setupInputValidation(document.getElementById('updateExpiryValue'), (value) => {
			const unit = document.getElementById('updateExpiryUnit').value;
			if (isNaN(value) || value <= 0) {
				return { id: 'updateExpiryValueError', message: 'Please enter a number.' };
			}
			if (!validateExpiryDate(value, unit)) {
				return { id: 'updateExpiryDateError', message: 'Please do not exceed 2099.12.31.' };
			}
			return null;
		});

        // 处理日期表单验证
        function validateExpiryDate(value, unit) {
            const maxDate = new Date('2099-12-31T23:59:59Z');
            let expiryDate = new Date();

            if (unit === 'permanent') {
                return true;
            }

            const numValue = parseInt(value);
            if (isNaN(numValue) || numValue <= 0) {
                return false;
            }

            switch (unit) {
                case 'days':
                    expiryDate.setUTCDate(expiryDate.getUTCDate() + numValue);
                    break;
                case 'months':
                    expiryDate.setUTCMonth(expiryDate.getUTCMonth() + numValue);
                    break;
                case 'years':
                    expiryDate.setUTCFullYear(expiryDate.getUTCFullYear() + numValue);
                    break;
            }

            return expiryDate <= maxDate;
        }

		// 处理表单提交事件
		const licenseForm = document.getElementById('licenseForm');
		if (licenseForm) {
			licenseForm.addEventListener('submit', async (e) => {
				e.preventDefault();

				if (!licenseForm.checkValidity()) {
					e.stopPropagation();
					licenseForm.classList.add('was-validated');
					return;
				}

				const formData = new FormData(e.target);
				const data = Object.fromEntries(formData);
				const username = selectedUsername || data.username;

				// 如果 MAC 地址为空，从数据中删除
				if (data.macAddress === '') {
					delete data.macAddress;
				}

				// 授权数量
				const authorizedCount = parseInt(data.authorizedCount);
				if (isNaN(authorizedCount) || authorizedCount <= 0) {
					window.showToast('Authorized Count must be a positive integer.', 'danger');
					return;
				}
				data.authorizedCount = authorizedCount;

				// 计算过期日期（UTC）
				let expiryDate = new Date();
				if (data.expiryUnit === 'permanent') {
					expiryDate = new Date('2099-12-31T23:59:59Z');
				} else {
					const value = parseInt(data.expiryValue);
					switch (data.expiryUnit) {
						case 'days': expiryDate.setUTCDate(expiryDate.getUTCDate() + value); break;
						case 'months': expiryDate.setUTCMonth(expiryDate.getUTCMonth() + value); break;
						case 'years': expiryDate.setUTCFullYear(expiryDate.getUTCFullYear() + value); break;
					}
				}
				data.expiryDate = expiryDate.toISOString().split('.')[0] + 'Z';

				try {
					const userId = await checkOrCreateUser(username);
					const licenseData = await generateLicense(userId, username, data.macAddress, data.expiryDate);
					if (!licenseData) {
						window.showToast('Failed to generate license', 'danger');
						return;
					}

					// 获得响应体请求存储接口并存储数据
					const response = await fetch('/store_license', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({
							...licenseData,
							authorized_count: data.authorizedCount,
							note: data.note
						}),
					});

					if (!response.ok) {
						const errorData = await response.json();
						if (errorData.error === 'MAC address already exists') {
							window.showToast('MAC address already exists. Please use a different MAC address.', 'warning');
						} else {
							throw new Error('Failed to store license');
						}
						return;
					}

					const apiResponse = await response.json();
					if (apiResponse.success) {
					  window.showToast('License generated and stored successfully', 'success');
					  window.loadLicenses();

					  // 重置表单
					  licenseForm.reset();
					  licenseForm.classList.remove('was-validated');

					  // 清除所有验证状态
					  licenseForm.querySelectorAll('.form-control, .form-select').forEach(element => {
						element.classList.remove('is-valid', 'is-invalid');
					  });

					  // 隐藏所有错误消息
					  licenseForm.querySelectorAll('.invalid-feedback').forEach(element => {
						element.style.display = 'none';
					  });

					  // 重置选中的用户名
					  selectedUsername = '';
					} else {
						throw new Error('Failed to store license');
					}
				} catch (error) {
					window.showToast(error.message, 'danger');
				}
			});
		}

		// 用户名输入框自动完成功能
		const usernameInput = document.getElementById('username');
		const usernameDropdown = document.getElementById('usernameDropdown');

		usernameInput.addEventListener('input', debounce(async (event) => {
			if (!event.target || !event.target.value) return;
			const searchTerm = event.target.value.trim();
			if (searchTerm.length < 2) {
				usernameDropdown.innerHTML = '';
				usernameDropdown.classList.remove('show');
				return;
			}

			try {
				const response = await fetch(\`/search_users?username=\${encodeURIComponent(searchTerm)}\`);
				if (!response.ok) throw new Error('Search failed');

				const searchedUsers = await response.json();
				usernameDropdown.innerHTML = '';

				if (searchedUsers.length > 0) {
					searchedUsers.forEach(user => {
						const item = document.createElement('li');
						item.className = 'list-group-item';
						item.textContent = user.username;
						item.addEventListener('click', function() {
							usernameInput.value = user.username;
							selectedUsername = user.username;
							usernameDropdown.classList.remove('show');
						});
						usernameDropdown.appendChild(item);
					});
					usernameDropdown.classList.add('show');
				} else {
					usernameDropdown.classList.remove('show');
				}
			} catch (error) {
				console.error('Error searching users:', error);
			}
		}, 300));

		// 检查用户是否存在，如果不存在则创建
		async function checkOrCreateUser(username) {
			const response = await fetch(\`/search_users?username=\${encodeURIComponent(username)}\`);
			const users = await response.json();

			if (users.length > 0) return users[0].id;

			const createResponse = await fetch('/create_user', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					username,
					created_at: new Date().toISOString().split('.')[0] + 'Z'
				}),
			});

			if (!createResponse.ok) throw new Error('Failed to create user');

			const newUser = await createResponse.json();
			return newUser.id;
		}

		// 调用外部 API 生成许可证
		async function generateLicense(userId, username, macAddress, expiryDate) {
			try {
				const response = await fetch('https://orion-api.lilh.net/generate_license', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ user_id: userId, username, mac_address: macAddress, expiry_date: expiryDate }),
				});

				if (!response.ok) throw new Error('API request failed');

				const apiResponse = await response.json();
				return {
					license_info: {
						id: apiResponse.license_info.id,
						user_id: apiResponse.license_info.user_id,
						username: apiResponse.license_info.username,
						mac_address: apiResponse.license_info.mac_address,
						expiry_date: apiResponse.license_info.expiry_date,
						created_at: apiResponse.license_info.created_at
					},
					encrypted_license: apiResponse.encrypted_license
				};
			} catch (error) {
				console.error('Error generating license:', error);
				window.showToast('Failed to generate license from API', 'danger');
				return null;
			}
		}

		// 用户搜索功能
		const userSearchInput = document.getElementById('userSearch');
		if (userSearchInput) {
			userSearchInput.addEventListener('input', debounce(async function(event) {
				const searchTerm = event.target.value.trim();

				if (searchTerm === '') {
					// 如果搜索框为空，重新加载所有用户
					await window.loadLicenses();
				} else {
					try {
						// 调用后端 API 进行搜索
						const response = await fetch(\`/search_users?username=\${encodeURIComponent(searchTerm)}\`);
						if (!response.ok) throw new Error('Search failed');

						const searchedUsers = await response.json();

						// 更新用户列表
						updateUserList(searchedUsers);
					} catch (error) {
						console.error('Error searching users:', error);
						window.showToast('Failed to search users', 'danger');
					}
				}
			}, 300)); // 300毫秒的延迟
		}

		// 更新用户列表的辅助函数
		function updateUserList(users) {
			const userList = document.getElementById('userList');
			if (userList) {
				userList.innerHTML = '';
				users.forEach(user => {
					const hasActiveLicenses = user.licenses && user.licenses.some(license => license.used_count > 0);
					const userItem = document.createElement('div');
					userItem.className = 'list-group-item list-group-item-action d-flex align-items-center';
					userItem.innerHTML = \`
						<div class="user-avatar me-3">\${user.username[0].toUpperCase()}</div>
						<div class="flex-grow-1">
							<h6 class="mb-0">\${user.username}</h6>
							<small>ID: \${user.id}</small>
							<div class="user-meta">
								<span><i class="bi bi-calendar"></i> \${window.formatDate(user.created_at)}</span>
							</div>
						</div>
						<div class="license-count" onclick="window.showUserLicenses('\${user.id}')">
							\${user.licenses ? user.licenses.length : 0} Licenses
							\${hasActiveLicenses ? \`
							<div class="ping-wrapper">
								<span class="animate-ping"></span>
								<span class="ping-core"></span>
							</div>
							\` : ''}
						</div>
					\`;
					userList.appendChild(userItem);
				});
			}
		}

		// 点击其他地方时隐藏下拉列表
		document.addEventListener('click', function(e) {
			if (!e.target.closest('#username') && !e.target.closest('#usernameDropdown')) {
				const dropdown = document.getElementById('usernameDropdown');
				if (dropdown) {
					dropdown.classList.remove('show');
				}
			}
		});

		// 处理 Expiration 选择
		const expiryValueInput = document.getElementById('expiryValue');
		const expiryUnitDropdown = document.getElementById('expiryUnitDropdown');
		const expiryUnitMenu = document.getElementById('expiryUnitMenu');
		const selectedExpiryUnit = document.getElementById('selectedExpiryUnit');
		const expiryUnitInput = document.getElementById('expiryUnit');

        if (expiryUnitMenu && expiryValueInput) {
            expiryUnitMenu.addEventListener('click', function(e) {
                e.preventDefault();
                if (e.target.classList.contains('dropdown-item')) {
                    const value = e.target.getAttribute('data-value');
                    const text = e.target.textContent;
                    selectedExpiryUnit.textContent = text;
                    expiryUnitInput.value = value;

                    const isPermanent = value === 'permanent';
                    expiryValueInput.disabled = isPermanent;
                    expiryValueInput.value = isPermanent ? '' : expiryValueInput.value;
                    expiryValueInput.toggleAttribute('required', !isPermanent);
                    expiryValueInput.dispatchEvent(new Event('input'));

                    // 重置错误消息
                    document.getElementById('expiryValueError').style.display = 'none';
                    document.getElementById('expiryDateError').style.display = 'none';

                    // 触发验证
                    expiryValueInput.dispatchEvent(new Event('input'));
                }
            });
        }

		// 加载许可证列表
		window.loadLicenses = async function() {
			const response = await fetch('/users_and_licenses');
			users = await response.json();
			licenses = users.flatMap(user => user.licenses.map(license => ({ ...license, username: user.username })));

			const userList = document.getElementById('userList');
			if (userList) {
				userList.innerHTML = '';

				users.forEach(user => {
					const hasActiveLicenses = user.licenses.some(license => license.used_count > 0);
					const userItem = document.createElement('div');
					userItem.className = 'list-group-item list-group-item-action d-flex align-items-center';
					userItem.innerHTML = \`
						<div class="user-avatar me-3">\${user.username[0].toUpperCase()}</div>
						<div class="flex-grow-1">
							<h6 class="mb-0">\${user.username}</h6>
							<small>ID: \${user.id}</small>
							<div class="user-meta">
								<span><i class="bi bi-calendar"></i> \${window.formatDate(user.created_at)}</span>
							</div>
						</div>
						<div class="license-count" onclick="window.showUserLicenses('\${user.id}')">
							\${user.licenses.length} Licenses
							\${hasActiveLicenses ? \`
							<div class="ping-wrapper">
								<span class="animate-ping"></span>
								<span class="ping-core"></span>
							</div>
							\` : ''}
						</div>
					\`;
					userList.appendChild(userItem);
				});
			}
		};

		// 显示用户许可证列表
		window.showUserLicenses = function(userId) {
			const user = users.find(u => u.id === userId);
			if (user) {
				const licenseQueryCount = document.getElementById('licenseQueryCount');
				const licenseResultList = document.getElementById('licenseResultList');

				const licenseCount = user.licenses ? user.licenses.length : 0;
				licenseQueryCount.textContent = \`\${user.username}'s Licenses (Total: \${licenseCount})\`;

				if (user.licenses && user.licenses.length > 0) {
					licenseResultList.innerHTML = user.licenses.map(license => window.generateLicenseResultItem(license)).join('');
				} else {
					licenseResultList.innerHTML = '<p>No licenses found for this user.</p>';
				}

				document.getElementById('licenseListModal').style.display = 'block';
			}
		};

		// 生成许可证结果项
		window.generateLicenseResultItem = function(license) {
			const status = getLicenseStatus(license.expiry_date);
			const statusClass = getLicenseStatusBadgeClass(license.expiry_date);

			return \`
				<div class="batch-modal-result-item">
					<div class="batch-modal-result-header">
						<div>
							<span class="batch-modal-ip-address clickable-item" onclick="window.copyToClipboard('\${license.id}')">\${license.id}</span>
							<span class="batch-modal-status badge \${statusClass}">\${status}</span>
						</div>
						<div class="batch-modal-as-info" onclick="window.toggleLicenseDetails(this)">View more info</div>
					</div>
					<div class="batch-modal-result-details">
						<div class="detail-column">
							<span class="detail-label">MAC Address</span>
							<span class="detail-value clickable-item" onclick="window.copyToClipboard('\${license.mac_address}')">\${license.mac_address || 'N/A'}</span>
						</div>
						<div class="detail-column">
							<span class="detail-label">Created At</span>
							<span class="detail-value">\${window.formatDate(license.created_at)}</span>
						</div>
						<div class="detail-column">
							<span class="detail-label">Expiry Date</span>
							<span class="detail-value">\${window.formatDate(license.expiry_date)}</span>
						</div>
						<div class="detail-column">
							<span class="detail-label">Authorized Count</span>
							<span class="detail-value">\${license.authorized_count}</span>
						</div>
						<div class="detail-column">
							<span class="detail-label">Used Count</span>
							<span class="detail-value">\${license.used_count}</span>
						</div>
						<div class="detail-column">
							<span class="detail-label">Actions</span>
							<span class="detail-value">
								<i class="bi bi-pencil-square" onclick="window.openUpdateModal('\${license.id}')"></i>
								<i class="bi bi-trash" onclick="window.deleteLicense('\${license.id}')"></i>
							</span>
						</div>
					</div>
					<div class="batch-modal-as-details" style="display: none;">
						<div class="license-content">
							<span class="encrypted-license clickable-item" onclick="window.copyToClipboard('\${license.encrypted_license}')">\${license.encrypted_license}</span>
						</div>
						\${license.note ? \`
						<div class="license-note">
							<span class="detail-value">\${license.note}</span>
						</div>
						\` : ''}
						<div class="license-request-logs" id="licenseRequestLogs-\${license.id}">
							<!-- 日志信息将通过 JavaScript 动态加载 -->
						</div>
					</div>
				</div>
				\`;
		}

		// 查询许可证请求日志
		window.loadLicenseRequestLogs = async function(licenseId) {
			try {
				const response = await fetch(\`/license_request_logs?license_id=\${licenseId}\`);
				if (!response.ok) throw new Error('Failed to fetch license request logs');
				const logs = await response.json();

				const logsHtml = logs.map(log => \`
					<div class="license-request-log-item">
						<span class="license-request-log-date">\${window.formatDate(log.request_date)}</span>  ------
						<span class="license-request-log-mac">\${log.mac_address}</span> ------
						<span class="license-request-log-ip">\${log.client_ip}</span>
					</div>
				\`).join('');

				document.getElementById(\`licenseRequestLogs-\${licenseId}\`).innerHTML = logsHtml || 'No request logs found.';
			} catch (error) {
				console.error('Error fetching license request logs:', error);
				document.getElementById(\`licenseRequestLogs-\${licenseId}\`).innerHTML = 'Failed to load request logs.';
			}
		}

		// 切换许可证详细信息的显示
		window.toggleLicenseDetails = function(element) {
			const resultItem = element.closest('.batch-modal-result-item');
			const details = resultItem.querySelector('.batch-modal-as-details');
			if (details) {
				if (details.style.display === 'none') {
					details.style.display = 'block';
					element.textContent = 'Hide more info';

					// 获取许可证 ID 并加载请求日志
					const licenseId = resultItem.querySelector('.batch-modal-ip-address').textContent;
					window.loadLicenseRequestLogs(licenseId);
				} else {
					details.style.display = 'none';
					element.textContent = 'View more info';
				}
			}
		};

		// 获取许可证状态
		function getLicenseStatus(expiryDate) {
			const now = new Date();
			const expiry = new Date(expiryDate);
			const diff = expiry.getTime() - now.getTime();
			const days = Math.ceil(diff / (1000 * 60 * 60 * 24));

			if (days < 0) {
				return 'Expired';
			} else if (days < 15) {
				return 'Expiring Soon';
			} else {
				return 'Active';
			}
		}

		// 获取许可证状态标签类名
		function getLicenseStatusBadgeClass(expiryDate) {
			const status = getLicenseStatus(expiryDate);
			switch (status) {
				case 'Expired': return 'bg-danger';
				case 'Expiring Soon': return 'bg-warning';
				case 'Active': return 'bg-success';
				default: return 'bg-secondary';
			}
		}

		// 关闭模态框
		document.querySelector('.batch-modal-close-button').addEventListener('click', function() {
			document.getElementById('licenseListModal').style.display = 'none';
		});

		// 显示许可证信息
		window.showLicenseInfo = function(licenseId) {
			const license = licenses.find(l => l.id === licenseId);
			if (license) {
				document.getElementById('licenseInfoContent').textContent = JSON.stringify(license, null, 2);
				new bootstrap.Modal(document.getElementById('licenseInfoModal')).show();
			}
		};

		// 复制到剪贴板功能
		window.copyToClipboard = function(text) {
			navigator.clipboard.writeText(text).then(() => {
				window.showToast('Copied to clipboard', 'success');
			}).catch(err => {
				console.error('Failed to copy:', err);
				window.showToast('Copy failed', 'error');
			});
		};

		// 处理 Update License 模态框中的 Expiration 选择
		const updateExpiryValueInput = document.getElementById('updateExpiryValue');
		const updateExpiryUnitDropdown = document.getElementById('updateExpiryUnitDropdown');
		const updateExpiryUnitMenu = document.getElementById('updateExpiryUnitMenu');
		const updateSelectedExpiryUnit = document.getElementById('updateSelectedExpiryUnit');
		const updateExpiryUnitInput = document.getElementById('updateExpiryUnit');

        if (updateExpiryUnitMenu && updateExpiryValueInput) {
            updateExpiryUnitMenu.addEventListener('click', function(e) {
                e.preventDefault();
                if (e.target.classList.contains('dropdown-item')) {
                    const value = e.target.getAttribute('data-value');
                    const text = e.target.textContent;
                    updateSelectedExpiryUnit.textContent = text;
                    updateExpiryUnitInput.value = value;

                    const isPermanent = value === 'permanent';
                    updateExpiryValueInput.disabled = isPermanent;
                    updateExpiryValueInput.value = isPermanent ? '' : updateExpiryValueInput.value;
                    updateExpiryValueInput.toggleAttribute('required', !isPermanent);

                    // 重置错误消息
                    document.getElementById('updateExpiryValueError').style.display = 'none';
                    document.getElementById('updateExpiryDateError').style.display = 'none';

                    // 触发验证
                    updateExpiryValueInput.dispatchEvent(new Event('input'));
                }
            });
        }

		// 打开更新模态框
		window.openUpdateModal = function(licenseId) {
			const license = licenses.find(l => l.id === licenseId);
			if (license) {
				const form = document.getElementById('updateLicenseForm');

				document.getElementById('updateLicenseId').value = license.id;
				document.getElementById('updateMacAddress').value = license.mac_address || '';
				document.getElementById('updateExpiryValue').value = '';
				document.getElementById('updateExpiryUnit').value = 'days';
				document.getElementById('updateAuthorizedCount').value = license.authorized_count;
				document.getElementById('updateNote').value = license.note || '';

				// 存储原始值
				form.querySelectorAll('input, textarea').forEach(input => {
					originalValues[input.id] = input.value;
				});

				// 重置表单验证状态
				form.classList.remove('was-validated');

				// 初始禁用更新按钮
				document.getElementById('updateLicenseButton').disabled = true;

				new bootstrap.Modal(document.getElementById('updateLicenseModal')).show();

				// 在模态框显示后，重新添加事件监听器
				setTimeout(() => {
					setupUpdateFormListeners();
				}, 100);
			} else {
				console.error('License not found:', licenseId);
				window.showToast('Error: License not found', 'danger');
			}
		};

		// 更新许可证
		window.updateLicense = async function() {
			const form = document.getElementById('updateLicenseForm');
			if (!form.checkValidity()) {
				form.classList.add('was-validated');
				return;
			}

			const id = document.getElementById('updateLicenseId').value;
			const macAddress = document.getElementById('updateMacAddress').value;
			const expiryValue = document.getElementById('updateExpiryValue').value;
			const expiryUnit = document.getElementById('updateExpiryUnit').value;
			const note = document.getElementById('updateNote').value;
			const authorizedCount = document.getElementById('updateAuthorizedCount').value;

			const originalLicense = licenses.find(l => l.id === id);
			const macChanged = (macAddress || '') !== (originalLicense.mac_address || '');
			const expiryChanged = expiryValue !== '' || expiryUnit === 'permanent';
			const authorizedCountChanged = parseInt(authorizedCount) !== originalLicense.authorized_count;
			const noteChanged = note !== originalLicense.note;

			let updatedData = {};

			if (noteChanged) {
				updatedData.note = note;
			}

			if (authorizedCountChanged) {
				updatedData.authorized_count = parseInt(authorizedCount);
			}

			let expiryDate = originalLicense.expiry_date; // 默认使用原始到期时间

			if (expiryChanged) {
				if (expiryUnit === 'permanent') {
					expiryDate = '2099-12-31T23:59:59Z';
				} else {
					const currentExpiry = new Date(originalLicense.expiry_date);
					const value = parseInt(expiryValue);
					switch (expiryUnit) {
						case 'days': currentExpiry.setUTCDate(currentExpiry.getUTCDate() + value); break;
						case 'months': currentExpiry.setUTCMonth(currentExpiry.getUTCMonth() + value); break;
						case 'years': currentExpiry.setUTCFullYear(currentExpiry.getUTCFullYear() + value); break;
					}
					expiryDate = currentExpiry.toISOString().split('.')[0] + 'Z';
				}
				updatedData.expiry_date = expiryDate;
			}

			if (macChanged) {
				updatedData.mac_address = macAddress;
			}

			if (macChanged || expiryChanged) {
				// 只有当 MAC 地址或过期时间改变时才生成新的许可证
				const newLicenseData = await generateLicense(
					originalLicense.user_id,
					originalLicense.username,
					macAddress || undefined,
					expiryDate
				);
				if (newLicenseData) {
					updatedData = {
						...updatedData,
						id: newLicenseData.license_info.id,
						expiry_date: newLicenseData.license_info.expiry_date,
						created_at: newLicenseData.license_info.created_at,
						encrypted_license: newLicenseData.encrypted_license,
					};
					// 只有当 MAC 地址不为空时才添加到 updatedData
					if (newLicenseData.license_info.mac_address) {
						updatedData.mac_address = newLicenseData.license_info.mac_address;
					}
				} else {
					throw new Error('Failed to generate new license');
				}
			}

			// 如果没有任何变化，不进行更新
			if (Object.keys(updatedData).length === 0) {
				window.showToast('No changes detected', 'info');
				bootstrap.Modal.getInstance(document.getElementById('updateLicenseModal')).hide();
				return;
			}

			try {
				const response = await fetch(\`/licenses/\${id}\`, {
					method: 'PUT',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(updatedData),
				});

				if (!response.ok) {
					throw new Error('Failed to update license');
				}

				window.showToast('License updated successfully', 'success');
				window.loadLicenses();
				bootstrap.Modal.getInstance(document.getElementById('updateLicenseModal')).hide();
			} catch (error) {
				window.showToast(error.message, 'danger');
			}
		};

		// 更新按钮的事件监听器
		const updateLicenseButton = document.getElementById('updateLicenseButton');
		if (updateLicenseButton) {
			updateLicenseButton.addEventListener('click', window.updateLicense);
		}

		// 表单输入监听器
		function setupUpdateFormListeners() {
			const form = document.getElementById('updateLicenseForm');
			const updateButton = document.getElementById('updateLicenseButton');

			form.querySelectorAll('input, textarea').forEach(input => {
				input.addEventListener('input', checkForChanges);
			});

			document.getElementById('updateExpiryUnitMenu').addEventListener('click', function(e) {
				if (e.target.classList.contains('dropdown-item')) {
					setTimeout(checkForChanges, 0);
				}
			});

			function checkForChanges() {
				let hasChanges = false;
				form.querySelectorAll('input, textarea').forEach(input => {
					if (input.value !== originalValues[input.id]) {
						hasChanges = true;
					}
				});
				updateButton.disabled = !hasChanges;
			}
		}

		// 页面加载完成后调用
		document.addEventListener('DOMContentLoaded', setupUpdateFormListeners);

		// 添加 debounce 函数
		function debounce(func, wait) {
			let timeout;
			return function executedFunction(...args) {
				const later = () => {
					clearTimeout(timeout);
					func(...args);
				};
				clearTimeout(timeout);
				timeout = setTimeout(later, wait);
			};
		}

		// 页面加载完成后加载许可证列表
		window.loadLicenses();
});
`;
