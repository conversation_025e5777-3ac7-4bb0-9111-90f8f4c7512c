import { Env, License, LicenseResponse, LicenseRequestLog } from './types';
import {
	storeLicense,
	updateLicense,
	deleteLicense,
	getUserByUsername,
	createUser,
	getUsersAndLicenses,
	checkMacAddressExists,
	getLicenseRequestLogs
} from './db';
import { clientScript, HTML } from './templates';

// 处理所有传入请求
export async function handleRequest(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);

	// 根据请求方法和路径分发到不同的处理函数
	switch (true) {
		case request.method === 'GET' && url.pathname === '/':
			return handleHome();
		case request.method === 'POST' && url.pathname === '/store_license':
			return handleStoreLicense(request, env);
		case request.method === 'GET' && url.pathname === '/users_and_licenses':
			return handleGetUsersAndLicenses(env);
		case request.method === 'PUT' && url.pathname.startsWith('/licenses/'):
			return handleUpdateLicense(request, url.pathname.split('/')[2], env);
		case request.method === 'DELETE' && url.pathname.startsWith('/licenses/'):
			return handleDeleteLicense(url.pathname.split('/')[2], env);
		case request.method === 'GET' && url.pathname === '/search_users':
			return handleSearchUsers(request, env);
		case request.method === 'POST' && url.pathname === '/create_user':
			return handleCreateUser(request, env);
		case request.method === 'GET' && url.pathname === '/check_mac_address':
			return handleCheckMacAddress(request, env);
		case request.method === 'GET' && url.pathname === '/license_request_logs':
			return handleGetLicenseRequestLogs(request, env);
		default:
			return new Response('Not Found', { status: 404 });
	}
}

// 处理首页请求
function handleHome(): Response {
	// 返回包含客户端脚本的HTML页面
	const fullHTML = HTML.replace('<script id="appScript"></script>', `<script id="appScript">${clientScript}</script>`);
	return new Response(fullHTML, {
		headers: { 'Content-Type': 'text/html' },
	});
}

// 处理检查 MAC 地址的请求
async function handleCheckMacAddress(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const macAddress = url.searchParams.get('mac');
	const excludeLicenseId = url.searchParams.get('exclude_id');

	if (!macAddress) {
		return new Response(JSON.stringify({ error: 'MAC address is required' }), {
			status: 400,
			headers: { 'Content-Type': 'application/json' },
		});
	}

	const exists = await checkMacAddressExists(macAddress, env, excludeLicenseId || undefined);
	return new Response(JSON.stringify({ exists }), {
		headers: { 'Content-Type': 'application/json' },
	});
}

// 处理存储许可证的请求
async function handleStoreLicense(request: Request, env: Env): Promise<Response> {
	try {
		const data = await request.json() as {
			license_info?: {
				id: string;
				user_id: string;
				username: string;
				mac_address: string;
				expiry_date: string;
				created_at: string;
			};
			encrypted_license?: string;
			note?: string;
			authorized_count?: number;
		};
		console.log('Received license data:', JSON.stringify(data, null, 2));

		// 验证数据结构
		if (!data || typeof data !== 'object' || !data.license_info || !data.encrypted_license) {
			return new Response(JSON.stringify({ success: false, error: 'Invalid license data structure' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		// 验证 authorized_count
		if (data.authorized_count === undefined || data.authorized_count <= 0) {
			return new Response(JSON.stringify({ success: false, error: 'Invalid or missing authorized_count' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		const licenseResponse: LicenseResponse = {
			license_info: {
				id: data.license_info.id,
				user_id: data.license_info.user_id,
				username: data.license_info.username,
				mac_address: data.license_info.mac_address,
				expiry_date: data.license_info.expiry_date,
				created_at: data.license_info.created_at
			},
			encrypted_license: data.encrypted_license
		};

		const note = data.note;
		const authorizedCount = data.authorized_count;

		const result = await storeLicense(licenseResponse, note, authorizedCount, env);
		return new Response(JSON.stringify(result), {
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error: unknown) {
		console.error('Error processing license storage:', error);
		if (error instanceof Error) {
			console.error('Error details:', error.stack);
		}
		return handleError(error);
	}
}

// 处理获取所有用户及其许可证的请求
async function handleGetUsersAndLicenses(env: Env): Promise<Response> {
	const usersAndLicenses = await getUsersAndLicenses(env);
	return new Response(JSON.stringify(usersAndLicenses), {
		headers: { 'Content-Type': 'application/json' },
	});
}

// 处理更新许可证的请求
async function handleUpdateLicense(request: Request, id: string, env: Env): Promise<Response> {
	try {
		const data = await request.json() as Partial<License>;

		// 验证 authorized_count
		if (data.authorized_count !== undefined && (data.authorized_count <= 0)) {
			return new Response(JSON.stringify({ success: false, error: 'Invalid authorized_count' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		// 更新许可证
		const result = await updateLicense(id, data, env);

		return new Response(JSON.stringify(result), {
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error: unknown) {
		console.error('Error processing license update:', error);
		return handleError(error);
	}
}

// 处理删除许可证的请求
async function handleDeleteLicense(id: string, env: Env): Promise<Response> {
	const result = await deleteLicense(id, env);
	return new Response(JSON.stringify(result), {
		headers: { 'Content-Type': 'application/json' },
	});
}

// 处理用户搜索请求
async function handleSearchUsers(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const username = url.searchParams.get('username');
	if (!username) {
		return new Response(JSON.stringify({ error: 'Username is required' }), {
			status: 400,
			headers: { 'Content-Type': 'application/json' },
		});
	}

	const users = await getUserByUsername(username, env);
	return new Response(JSON.stringify(users), {
		headers: { 'Content-Type': 'application/json' },
	});
}

// 处理创建用户请求
async function handleCreateUser(request: Request, env: Env): Promise<Response> {
	try {
		const body = await request.json();
		if (typeof body !== 'object' || body === null || !('username' in body) || typeof body.username !== 'string' || !('created_at' in body) || typeof body.created_at !== 'string') {
			return new Response(JSON.stringify({ error: 'Invalid request body' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		const { username, created_at } = body;
		if (!username || !created_at) {
			return new Response(JSON.stringify({ error: 'Username and created_at are required' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		const newUser = await createUser(username, created_at, env);
		return new Response(JSON.stringify(newUser), {
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error: unknown) {
		console.error('Error creating user:', error);
		return handleError(error);
	}
}

// 处理获取许可证请求日志的请求
async function handleGetLicenseRequestLogs(request: Request, env: Env): Promise<Response> {
	const url = new URL(request.url);
	const licenseId = url.searchParams.get('license_id');

	if (!licenseId) {
		return new Response(JSON.stringify({ error: 'License ID is required' }), {
			status: 400,
			headers: { 'Content-Type': 'application/json' },
		});
	}

	try {
		const logs = await getLicenseRequestLogs(licenseId, env);
		return new Response(JSON.stringify(logs), {
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error) {
		console.error('Error fetching license request logs:', error);
		return handleError(error);
	}
}

// 统一错误处理
function handleError(error: unknown): Response {
	if (error instanceof Error) {
		return new Response(JSON.stringify({ error: error.message }), {
			status: 500,
			headers: { 'Content-Type': 'application/json' },
		});
	} else {
		return new Response(JSON.stringify({ error: 'Unknown error occurred' }), {
			status: 500,
			headers: { 'Content-Type': 'application/json' },
		});
	}
}
