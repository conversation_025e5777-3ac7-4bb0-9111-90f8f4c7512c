// 创建 users 表
wrangler d1 execute workers-orion --remote --command="
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  created_at TEXT NOT NULL
);"

// 为 users 表创建索引
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_users_username ON users(username);"

// 创建 licenses 表
wrangler d1 execute workers-orion --remote --command="
CREATE TABLE licenses (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  mac_address TEXT,
  expiry_date TEXT NOT NULL,
  created_at TEXT NOT NULL,
  encrypted_license TEXT NOT NULL,
  authorized_count INTEGER NOT NULL DEFAULT 1,
  used_count INTEGER NOT NULL DEFAULT 0,
  note TEXT,
  FOREIGN KEY (user_id) REFERENCES users(id)
);"

// 为 licenses 表创建索引
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_licenses_id ON licenses(id);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_licenses_user_id ON licenses(user_id);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_licenses_mac_address ON licenses(mac_address);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_licenses_expiry_date ON licenses(expiry_date);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_licenses_encrypted_license ON licenses(encrypted_license);"

// 创建 license_requests 表
wrangler d1 execute workers-orion --remote --command="
CREATE TABLE license_requests (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  license_id TEXT NOT NULL,
  mac_address TEXT NOT NULL,
  client_ip TEXT NOT NULL,
  request_date TEXT NOT NULL,
  FOREIGN KEY (license_id) REFERENCES licenses(id)
);"

// 为 license_requests 表创建索引
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_license_requests_license_id ON license_requests(license_id);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_license_requests_mac_address ON license_requests(mac_address);"
wrangler d1 execute workers-orion --remote --command="CREATE INDEX idx_license_requests_request_date ON license_requests(request_date);"
