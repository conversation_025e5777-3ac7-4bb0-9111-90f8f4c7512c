import { Env, User, License, LicenseResponse, LicenseRequestLog } from './types';

// 辅助函数：标准化日期格式
function standardizeDate(dateString: string): string {
	// 移除毫秒部分
	let standardizedDate = dateString.split('.')[0];
	// 确保以 'Z' 结尾
	if (!standardizedDate.endsWith('Z')) {
		standardizedDate += 'Z';
	}
	return standardizedDate;
}

// 创建新用户
export async function createUser(username: string, createdAt: string, env: Env): Promise<User> {
	const id = crypto.randomUUID();
	const created_at = standardizeDate(new Date(createdAt).toISOString());

	await env.DB.prepare(
		'INSERT INTO users (id, username, created_at) VALUES (?, ?, ?)'
	).bind(id, username, created_at).run();

	return { id, username, created_at };
}

// 根据用户名获取用户（支持模糊搜索）
export async function getUserByUsername(username: string, env: Env): Promise<User[]> {
	const { results } = await env.DB.prepare(`
        SELECT u.id, u.username, u.created_at,
               l.id as license_id, l.mac_address, l.expiry_date, l.created_at as license_created_at,
               l.encrypted_license, l.authorized_count, l.used_count, l.note
        FROM users u
        LEFT JOIN licenses l ON u.id = l.user_id
        WHERE u.username LIKE ?
        ORDER BY u.created_at DESC, l.created_at DESC
    `).bind(`%${username}%`).all();

	const users: User[] = [];
	let currentUser: User | null = null;

	results.forEach(row => {
		if (!currentUser || currentUser.id !== row.id) {
			currentUser = {
				id: row.id as string,
				username: row.username as string,
				created_at: row.created_at as string,
				licenses: [] // 初始化为空数组
			};
			users.push(currentUser);
		}

		if (row.license_id) {
			if (!currentUser.licenses) {
				currentUser.licenses = [];
			}

			const license: License = {
				id: row.license_id as string,
				user_id: row.id as string,
				mac_address: row.mac_address as string,
				expiry_date: row.expiry_date as string,
				created_at: row.license_created_at as string,
				encrypted_license: row.encrypted_license as string,
				authorized_count: row.authorized_count as number,
				used_count: row.used_count as number,
				note: row.note as string | undefined
			};

			currentUser.licenses.push(license);
		}
	});

	return users;
}

// 检查MAC地址是否已存在
export async function checkMacAddressExists(macAddress: string, env: Env, excludeLicenseId?: string): Promise<boolean> {
	let query = 'SELECT COUNT(*) as count FROM licenses WHERE mac_address = ?';
	const bindValues: any[] = [macAddress];

	if (excludeLicenseId) {
		query += ' AND id != ?';
		bindValues.push(excludeLicenseId);
	}

	const { results } = await env.DB.prepare(query).bind(...bindValues).all();
	return (results[0] as { count: number }).count > 0;
}

// 存储许可证信息到 D1 数据库
export async function storeLicense(licenseResponse: LicenseResponse, note: string | undefined, authorizedCount: number, env: Env): Promise<{ success: boolean; error?: string }> {
	console.log('Storing license, received data:', JSON.stringify(licenseResponse, null, 2));
	console.log('Authorized Count:', authorizedCount);
	console.log('Note:', note);

	if (!licenseResponse || !licenseResponse.license_info) {
		return { success: false, error: 'Invalid license response structure' };
	}

	const {
		id,
		user_id,
		mac_address,
		expiry_date,
		created_at
	} = licenseResponse.license_info;

	if (!id || !user_id || !expiry_date || !created_at) {
		return { success: false, error: 'Missing required fields in license info' };
	}

	const formattedExpiryDate = standardizeDate(expiry_date);
	const formattedCreatedAt = standardizeDate(created_at);

	try {
		await env.DB.prepare(
			'INSERT INTO licenses (id, user_id, mac_address, expiry_date, created_at, encrypted_license, authorized_count, used_count, note) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
		).bind(
			id,
			user_id,
			mac_address,
			formattedExpiryDate,
			formattedCreatedAt,
			licenseResponse.encrypted_license,
			authorizedCount,
			0,  // used_count 初始值为 0
			note || null
		).run();

		return { success: true };
	} catch (error) {
		console.error('Error storing license:', error);
		return { success: false, error: 'Failed to store license in database' };
	}
}

// 获取所有用户及其许可证信息
export async function getUsersAndLicenses(env: Env): Promise<User[]> {
	const { results } = await env.DB.prepare(`
        SELECT u.id, u.username, u.created_at,
               l.id as license_id, l.mac_address, l.expiry_date, l.created_at as license_created_at,
               l.encrypted_license, l.authorized_count, l.used_count, l.note
        FROM users u
        LEFT JOIN licenses l ON u.id = l.user_id
        ORDER BY u.created_at DESC, l.created_at DESC
    `).all();

	const users: User[] = [];
	let currentUser: User | null = null;

	results.forEach(row => {
		if (!currentUser || currentUser.id !== row.id) {
			currentUser = {
				id: row.id as string,
				username: row.username as string,
				created_at: row.created_at as string,
				licenses: []  // 初始化为空数组
			};
			users.push(currentUser);
		}

		if (row.license_id) {
			if (!currentUser.licenses) {
				currentUser.licenses = [];
			}

			currentUser.licenses.push({
				id: row.license_id as string,
				user_id: row.id as string,
				mac_address: row.mac_address as string,
				expiry_date: row.expiry_date as string,
				created_at: row.license_created_at as string,
				encrypted_license: row.encrypted_license as string,
				authorized_count: row.authorized_count as number,
				used_count: row.used_count as number,
				note: row.note as string | undefined
			});
		}
	});

	return users;
}

// 更新许可证信息
export async function updateLicense(id: string, data: Partial<License>, env: Env): Promise<{ success: boolean; error?: string }> {
	const updateFields: string[] = [];
	const bindValues: any[] = [];

	if (data.id !== undefined) {
		updateFields.push('id = ?');
		bindValues.push(data.id);
	}
	if (data.mac_address !== undefined) {
		updateFields.push('mac_address = ?');
		bindValues.push(data.mac_address);
	}
	if (data.expiry_date !== undefined) {
		updateFields.push('expiry_date = ?');
		bindValues.push(standardizeDate(data.expiry_date));
	}
	if (data.created_at !== undefined) {
		updateFields.push('created_at = ?');
		bindValues.push(standardizeDate(data.created_at));
	}
	if (data.encrypted_license !== undefined) {
		updateFields.push('encrypted_license = ?');
		bindValues.push(data.encrypted_license);
	}
	if (data.authorized_count !== undefined) {
		updateFields.push('authorized_count = ?');
		bindValues.push(data.authorized_count);
	}
	if (data.used_count !== undefined) {
		updateFields.push('used_count = ?');
		bindValues.push(data.used_count);
	}
	if (data.note !== undefined) {
		updateFields.push('note = ?');
		bindValues.push(data.note);
	}

	if (updateFields.length === 0) {
		return { success: false, error: 'No fields to update' };
	}

	const query = `UPDATE licenses SET ${updateFields.join(', ')} WHERE id = ?`;
	bindValues.push(id);

	try {
		await env.DB.prepare(query)
			.bind(...bindValues)
			.run();
		return { success: true };
	} catch (error) {
		console.error('Error updating license:', error);
		return { success: false, error: 'Failed to update license in database' };
	}
}

// 删除指定 ID 的许可证及相关日志数据
export async function deleteLicense(id: string, env: Env): Promise<{ success: boolean }> {
	try {
		// 使用批处理 API 确保多个删除操作作为一个原子操作执行
		await env.DB.batch([
			env.DB.prepare('DELETE FROM license_requests WHERE license_id = ?').bind(id),
			env.DB.prepare('DELETE FROM licenses WHERE id = ?').bind(id)
		]);

		return { success: true };
	} catch (error) {
		console.error('Error deleting license:', error);
		return { success: false };
	}
}

// 根据 license_id 获取许可证请求日志信息
export async function getLicenseRequestLogs(licenseId: string, env: Env): Promise<LicenseRequestLog[]> {
	const { results } = await env.DB.prepare(`
    SELECT mac_address, client_ip, request_date
    FROM license_requests
    WHERE license_id = ?
    ORDER BY request_date DESC
  `).bind(licenseId).all();

	return results.map(row => ({
		mac_address: row.mac_address as string,
		client_ip: row.client_ip as string,
		request_date: row.request_date as string
	}));
}
